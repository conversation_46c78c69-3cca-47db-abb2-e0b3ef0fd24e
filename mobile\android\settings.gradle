rootProject.name = 'AutoflowMobileTemp'

// Apply native modules settings if the file exists
def nativeModulesFile = file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
if (nativeModulesFile.exists()) {
    apply from: nativeModulesFile
    applyNativeModulesSettingsGradle(settings)
}

include ':app'

// Include React Native Gradle plugin if it exists
def gradlePluginDir = file("../node_modules/@react-native/gradle-plugin")
if (gradlePluginDir.exists()) {
    includeBuild('../node_modules/@react-native/gradle-plugin')
}
