import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY, SHADOWS } from '../constants/theme';

type Screen = 'Home' | 'Shop' | 'Search' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

const HomeScreen: React.FC<Props> = ({ onNavigate }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = () => {
    if (searchQuery.trim()) {
      console.log('Search query:', searchQuery);
      onNavigate('Search');
    }
  };

  const handleCall = () => {
    console.log('Call +254724288400');
    // TODO: Implement call functionality
  };

  const handleLogin = () => {
    onNavigate('Profile');
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.logo}>Autoflow</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.phoneButton} onPress={handleCall}>
            <Text style={styles.phoneIcon}>📞</Text>
            <Text style={styles.phoneText}>+254724288400</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginIcon}>→</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Content with Background */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Background Pattern Overlay */}
        <View style={styles.backgroundPattern} />

        {/* Hero Section with exact text placement */}
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>Find your VW and Audi Parts...</Text>
          <Text style={styles.heroSubtitle}>
            We specialize in VW and Audi Spare parts in Nairobi. Give us a call on +254724288400
          </Text>

          {/* Decorative car elements */}
          <View style={styles.carDecorations}>
            <View style={styles.carSilhouette1} />
            <View style={styles.carSilhouette2} />
          </View>
        </View>

        {/* Search Section - positioned like in web version */}
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search parts..."
              placeholderTextColor="#999"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
              <Text style={styles.searchButtonText}>Search</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0f1a', // Very dark background like the web version
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    paddingTop: 40, // Status bar padding
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent header
  },
  logo: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  phoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  phoneIcon: {
    fontSize: TYPOGRAPHY.sizes.md,
    marginRight: SPACING.xs,
  },
  phoneText: {
    color: '#ffffff',
    fontSize: TYPOGRAPHY.sizes.sm,
  },
  loginButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginIcon: {
    color: COLORS.PRIMARY,
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
  },
  content: {
    flex: 1,
    // Create the dark automotive background effect with subtle patterns
    backgroundColor: '#0a0f1a',
    // Add subtle texture/pattern effect
    position: 'relative',
  },
  heroSection: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl * 4, // More vertical space like web version
    alignItems: 'center',
    minHeight: 400, // Ensure enough space for the hero content
    justifyContent: 'center',
  },
  heroTitle: {
    fontSize: 32, // Larger title like web version
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 42,
    letterSpacing: 0.5,
  },
  heroSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: '#cccccc',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.xl * 2, // More space before search
  },
  searchSection: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingBottom: SPACING.xl * 2, // More bottom padding
  },
  searchContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4, // Add shadow on Android
    shadowColor: '#000', // Add shadow on iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.lg, // Larger padding like web version
    paddingHorizontal: SPACING.lg,
    fontSize: TYPOGRAPHY.sizes.md,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  searchButton: {
    backgroundColor: '#e67e22', // Orange color matching web version
    paddingHorizontal: SPACING.xl * 1.5, // Wider button
    paddingVertical: SPACING.lg,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100, // Ensure button is wide enough
  },
  searchButtonText: {
    color: '#ffffff',
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#0a0f1a',
    opacity: 0.9,
  },
  carDecorations: {
    position: 'absolute',
    top: '60%',
    left: 0,
    right: 0,
    height: 200,
    zIndex: -1,
  },
  carSilhouette1: {
    position: 'absolute',
    left: '10%',
    top: 50,
    width: 120,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 20,
    transform: [{ skewX: '-15deg' }],
  },
  carSilhouette2: {
    position: 'absolute',
    right: '10%',
    top: 80,
    width: 100,
    height: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    borderRadius: 18,
    transform: [{ skewX: '10deg' }],
  },
});

export default HomeScreen;
