import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

type Screen = 'Home' | 'Login' | 'Parts' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

const HomeScreen: React.FC<Props> = ({ onNavigate }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome to AutoFlow</Text>
      <Text style={styles.subtitle}>Parts Management System</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => onNavigate('Parts')}
        >
          <Text style={styles.buttonText}>🔧 Browse Parts</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => onNavigate('Login')}
        >
          <Text style={styles.buttonText}>🔐 Login</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => onNavigate('Profile')}
        >
          <Text style={styles.buttonText}>👤 Profile</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.message}>
        🎉 Welcome to AutoFlow! 🎉
      </Text>

      <Text style={styles.info}>
        Your one-stop solution for VW and Audi parts
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#666666',
    marginBottom: 40,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  message: {
    fontSize: 16,
    color: '#4CAF50',
    textAlign: 'center',
    marginTop: 20,
  },
  info: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginTop: 10,
    fontStyle: 'italic',
  },
});

export default HomeScreen;
