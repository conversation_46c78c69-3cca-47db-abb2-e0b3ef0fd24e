import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY, SHADOWS } from '../constants/theme';

type Screen = 'Home' | 'Shop' | 'Search' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

const HomeScreen: React.FC<Props> = ({ onNavigate }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = () => {
    if (searchQuery.trim()) {
      console.log('Search query:', searchQuery);
      onNavigate('Search');
    }
  };

  const handleCall = () => {
    console.log('Call +254724288400');
    // TODO: Implement call functionality
  };

  const handleLogin = () => {
    onNavigate('Profile');
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.logo}>Autoflow</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.phoneButton} onPress={handleCall}>
            <Text style={styles.phoneIcon}>📞</Text>
            <Text style={styles.phoneText}>+254724288400</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginIcon}>→</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>Find your VW and Audi Parts...</Text>
          <Text style={styles.heroSubtitle}>
            We specialize in VW and Audi Spare parts in Nairobi. Give us a call on +254724288400
          </Text>
        </View>

        {/* Car Images Section */}
        <View style={styles.carImagesSection}>
          {/* VW Logo */}
          <View style={styles.carBrand}>
            <Text style={styles.vwLogo}>VW</Text>
          </View>

          {/* Audi Logo */}
          <View style={styles.carBrand}>
            <Text style={styles.audiLogo}>AUDI</Text>
          </View>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search parts..."
              placeholderTextColor="#999"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
              <Text style={styles.searchButtonText}>Search</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity onPress={() => {}}>
          <Text style={styles.footerLink}>About</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onNavigate('Shop')}>
          <Text style={styles.footerLink}>View All Parts</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => {}}>
          <Text style={styles.footerLink}>Privacy</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => {}}>
          <Text style={styles.footerLink}>Terms</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleLogin}>
          <Text style={styles.footerLink}>Login</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a2332', // Dark blue-gray background
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    paddingTop: 40, // Status bar padding
  },
  logo: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  phoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  phoneIcon: {
    fontSize: TYPOGRAPHY.sizes.md,
    marginRight: SPACING.xs,
  },
  phoneText: {
    color: '#ffffff',
    fontSize: TYPOGRAPHY.sizes.sm,
  },
  loginButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginIcon: {
    color: COLORS.PRIMARY,
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl * 2,
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: TYPOGRAPHY.sizes.xxxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 40,
  },
  heroSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: '#cccccc',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: SPACING.md,
  },
  carImagesSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  carBrand: {
    alignItems: 'center',
  },
  vwLogo: {
    fontSize: 48,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
    letterSpacing: 4,
  },
  audiLogo: {
    fontSize: 32,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: '#ffffff',
    letterSpacing: 2,
  },
  searchSection: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
  },
  searchContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    fontSize: TYPOGRAPHY.sizes.md,
    color: '#333333',
  },
  searchButton: {
    backgroundColor: '#e67e22', // Orange color from the image
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchButtonText: {
    color: '#ffffff',
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.md,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  footerLink: {
    color: '#cccccc',
    fontSize: TYPOGRAPHY.sizes.sm,
  },
});

export default HomeScreen;
