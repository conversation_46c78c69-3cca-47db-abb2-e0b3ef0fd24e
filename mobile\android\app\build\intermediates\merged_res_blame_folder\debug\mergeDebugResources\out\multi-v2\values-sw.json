{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "35,36,37,38,39,40,41,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3102,3196,3298,3395,3496,3603,3710,9950", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3191,3293,3390,3491,3598,3705,3820,10046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,287,386,515,598,666,758,831,894,980,1043,1108,1176,1239,1293,1425,1482,1544,1598,1672,1810,1891,1971,2073,2158,2245,2333,2400,2466,2538,2620,2710,2782,2857,2928,3001,3098,3172,3267,3364,3438,3523,3623,3676,3744,3832,3922,3984,4048,4111,4228,4338,4449,4561", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "209,282,381,510,593,661,753,826,889,975,1038,1103,1171,1234,1288,1420,1477,1539,1593,1667,1805,1886,1966,2068,2153,2240,2328,2395,2461,2533,2615,2705,2777,2852,2923,2996,3093,3167,3262,3359,3433,3518,3618,3671,3739,3827,3917,3979,4043,4106,4223,4333,4444,4556,4637"}, "to": {"startLines": "2,34,43,44,45,47,48,50,53,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3029,3917,4016,4145,4314,4382,4545,4770,4902,4988,5051,5116,5184,5247,5301,5433,5490,5552,5606,5680,6050,6131,6211,6313,6398,6485,6573,6640,6706,6778,6860,6950,7022,7097,7168,7241,7338,7412,7507,7604,7678,7763,7863,7916,7984,8072,8162,8224,8288,8351,8468,8578,8689,8801", "endLines": "5,34,43,44,45,47,48,50,53,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "259,3097,4011,4140,4223,4377,4469,4613,4828,4983,5046,5111,5179,5242,5296,5428,5485,5547,5601,5675,5813,6126,6206,6308,6393,6480,6568,6635,6701,6773,6855,6945,7017,7092,7163,7236,7333,7407,7502,7599,7673,7758,7858,7911,7979,8067,8157,8219,8283,8346,8463,8573,8684,8796,8877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,367,466,574,664,769,886,969,1051,1142,1235,1330,1424,1524,1617,1712,1806,1897,1988,2070,2171,2279,2378,2485,2597,2701,2863,9210", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "362,461,569,659,764,881,964,1046,1137,1230,1325,1419,1519,1612,1707,1801,1892,1983,2065,2166,2274,2373,2480,2592,2696,2858,2955,9288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,216,302,373,442,525,594,662,741,826,909,992,1064,1154,1244,1323,1406,1490,1572,1648,1724,1811,1886,1969,2044", "endColumns": "68,91,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,211,297,368,437,520,589,657,736,821,904,987,1059,1149,1239,1318,1401,1485,1567,1643,1719,1806,1881,1964,2039,2117"}, "to": {"startLines": "33,42,46,49,51,52,54,67,68,69,104,105,106,107,109,110,111,112,113,114,115,116,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,3825,4228,4474,4618,4687,4833,5818,5886,5965,8882,8965,9048,9120,9293,9383,9462,9545,9629,9711,9787,9863,10051,10126,10209,10284", "endColumns": "68,91,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3024,3912,4309,4540,4682,4765,4897,5881,5960,6045,8960,9043,9115,9205,9378,9457,9540,9624,9706,9782,9858,9945,10121,10204,10279,10357"}}]}]}