{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,400,492,618,699,764,863,939,1000,1089,1156,1210,1278,1338,1392,1509,1569,1631,1685,1757,1879,1963,2055,2162,2240,2322,2410,2477,2543,2615,2692,2776,2848,2925,2999,3070,3158,3229,3322,3417,3491,3565,3661,3713,3780,3866,3954,4016,4080,4143,4253,4349,4448,4546", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "318,395,487,613,694,759,858,934,995,1084,1151,1205,1273,1333,1387,1504,1564,1626,1680,1752,1874,1958,2050,2157,2235,2317,2405,2472,2538,2610,2687,2771,2843,2920,2994,3065,3153,3224,3317,3412,3486,3560,3656,3708,3775,3861,3949,4011,4075,4138,4248,4344,4443,4541,4620"}, "to": {"startLines": "2,36,46,47,48,50,51,53,56,58,59,60,61,62,63,64,65,66,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3139,4138,4230,4356,4520,4585,4755,4989,5118,5207,5274,5328,5396,5456,5510,5627,5687,5749,5803,5875,6228,6312,6404,6511,6589,6671,6759,6826,6892,6964,7041,7125,7197,7274,7348,7419,7507,7578,7671,7766,7840,7914,8010,8062,8129,8215,8303,8365,8429,8492,8602,8698,8797,8895", "endLines": "7,36,46,47,48,50,51,53,56,58,59,60,61,62,63,64,65,66,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "368,3211,4225,4351,4432,4580,4679,4826,5045,5202,5269,5323,5391,5451,5505,5622,5682,5744,5798,5870,5992,6307,6399,6506,6584,6666,6754,6821,6887,6959,7036,7120,7192,7269,7343,7414,7502,7573,7666,7761,7835,7909,8005,8057,8124,8210,8298,8360,8424,8487,8597,8693,8792,8890,8969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,229,318,401,472,543,630,698,768,847,929,1015,1101,1171,1247,1324,1406,1487,1569,1644,1715,1785,1869,1942,2020,2091", "endColumns": "71,101,88,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "122,224,313,396,467,538,625,693,763,842,924,1010,1096,1166,1242,1319,1401,1482,1564,1639,1710,1780,1864,1937,2015,2086,2166"}, "to": {"startLines": "35,44,45,49,52,54,55,57,70,71,72,107,108,109,110,112,113,114,115,116,117,118,119,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3067,3947,4049,4437,4684,4831,4902,5050,5997,6067,6146,8974,9060,9146,9216,9375,9452,9534,9615,9697,9772,9843,9913,10098,10171,10249,10320", "endColumns": "71,101,88,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "3134,4044,4133,4515,4750,4897,4984,5113,6062,6141,6223,9055,9141,9211,9287,9447,9529,9610,9692,9767,9838,9908,9992,10166,10244,10315,10395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "37,38,39,40,41,42,43,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3216,3314,3416,3517,3616,3721,3828,9997", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3309,3411,3512,3611,3716,3823,3942,10093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "373,480,582,692,778,883,1000,1078,1154,1245,1338,1433,1527,1621,1714,1809,1906,1997,2088,2172,2276,2388,2487,2593,2704,2806,2969,9292", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "475,577,687,773,878,995,1073,1149,1240,1333,1428,1522,1616,1709,1804,1901,1992,2083,2167,2271,2383,2482,2588,2699,2801,2964,3062,9370"}}]}]}