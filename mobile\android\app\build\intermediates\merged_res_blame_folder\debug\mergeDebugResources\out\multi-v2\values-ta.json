{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-35:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,326,406,478,546,630,700,767,843,921,1003,1083,1154,1236,1318,1396,1485,1575,1656,1728,1798,1892,1967,2050,2119", "endColumns": "74,104,90,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,230,321,401,473,541,625,695,762,838,916,998,1078,1149,1231,1313,1391,1480,1570,1651,1723,1793,1887,1962,2045,2114,2192"}, "to": {"startLines": "33,39,40,44,47,49,50,52,66,67,68,106,107,108,109,111,112,113,114,115,116,117,118,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3088,3618,3723,4125,4377,4517,4585,4730,5842,5909,5985,9065,9147,9227,9298,9462,9544,9622,9711,9801,9882,9954,10024,10219,10294,10377,10446", "endColumns": "74,104,90,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "3158,3718,3809,4200,4444,4580,4664,4795,5904,5980,6058,9142,9222,9293,9375,9539,9617,9706,9796,9877,9949,10019,10113,10289,10372,10441,10519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,9380", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,9457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "10118", "endColumns": "100", "endOffsets": "10214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\603752beef54a2060376812c2cf342db\\transformed\\material-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1102,1210,1278,1339,1447,1514,1600,1658,1742,1809,1863,1986,2048,2111,2165,2253,2381,2467,2549,2681,2761,2842,2931,2988,3040,3106,3191,3279,3371,3440,3517,3597,3665,3764,3847,3939,4033,4107,4193,4287,4337,4403,4488,4575,4638,4703,4766,4874,4977,5075,5180,5241,5297", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "267,355,441,525,628,722,831,949,1033,1097,1205,1273,1334,1442,1509,1595,1653,1737,1804,1858,1981,2043,2106,2160,2248,2376,2462,2544,2676,2756,2837,2926,2983,3035,3101,3186,3274,3366,3435,3512,3592,3660,3759,3842,3934,4028,4102,4188,4282,4332,4398,4483,4570,4633,4698,4761,4869,4972,5070,5175,5236,5292,5378"}, "to": {"startLines": "2,34,35,36,37,38,41,42,43,45,46,48,51,53,54,55,56,57,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3163,3251,3337,3421,3524,3814,3923,4041,4205,4269,4449,4669,4800,4908,4975,5061,5119,5203,5270,5324,5447,5509,5572,5626,5714,6063,6149,6231,6363,6443,6524,6613,6670,6722,6788,6873,6961,7053,7122,7199,7279,7347,7446,7529,7621,7715,7789,7875,7969,8019,8085,8170,8257,8320,8385,8448,8556,8659,8757,8862,8923,8979", "endLines": "5,34,35,36,37,38,41,42,43,45,46,48,51,53,54,55,56,57,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,88,56,51,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,60,55,85", "endOffsets": "317,3246,3332,3416,3519,3613,3918,4036,4120,4264,4372,4512,4725,4903,4970,5056,5114,5198,5265,5319,5442,5504,5567,5621,5709,5837,6144,6226,6358,6438,6519,6608,6665,6717,6783,6868,6956,7048,7117,7194,7274,7342,7441,7524,7616,7710,7784,7870,7964,8014,8080,8165,8252,8315,8380,8443,8551,8654,8752,8857,8918,8974,9060"}}]}]}