1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autoflowmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
12-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
13-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
14    <!--
15    This manifest file is used only by Gradle to configure debug-only capabilities
16    for React Native Apps.
17    -->
18    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
18-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
18-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
19
20    <permission
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
21        android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-24:19
27        android:name="com.autoflowmobile.MainApplication"
27-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:7-38
28        android:allowBackup="false"
28-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:7-34
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
30        android:debuggable="true"
31        android:extractNativeLibs="true"
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:8:7-41
33        android:label="@string/app_name"
33-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:7:7-39
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:7-52
35        android:theme="@style/AppTheme"
35-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:11:7-38
36        android:usesCleartextTraffic="true" >
36-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
37        <activity
37-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:7-23:18
38            android:name="com.autoflowmobile.MainActivity"
38-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:9-37
39            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
39-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:9-118
40            android:exported="true"
40-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:9-32
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:9-41
42            android:launchMode="singleTask"
42-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:9-40
43            android:windowSoftInputMode="adjustResize" >
43-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:9-51
44            <intent-filter>
44-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:9-22:25
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:13-65
45-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:21-62
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:13-73
47-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:23-70
48            </intent-filter>
49        </activity>
50
51        <provider
51-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
52            android:name="com.imagepicker.ImagePickerProvider"
52-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
53            android:authorities="com.autoflowmobile.imagepickerprovider"
53-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
54            android:exported="false"
54-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
55            android:grantUriPermissions="true" >
55-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
56            <meta-data
56-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
57                android:name="android.support.FILE_PROVIDER_PATHS"
57-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
58                android:resource="@xml/imagepicker_provider_paths" />
58-->[:react-native-image-picker] C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
59        </provider>
60
61        <activity
61-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
62            android:name="com.facebook.react.devsupport.DevSettingsActivity"
62-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
63            android:exported="false" />
63-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
64
65        <meta-data
65-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\6cd76716028482114684035a34d67809\transformed\jetified-okhttp3-integration-4.12.0\AndroidManifest.xml:11:9-13:43
66            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
66-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\6cd76716028482114684035a34d67809\transformed\jetified-okhttp3-integration-4.12.0\AndroidManifest.xml:12:13-84
67            android:value="GlideModule" />
67-->[com.github.bumptech.glide:okhttp3-integration:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\6cd76716028482114684035a34d67809\transformed\jetified-okhttp3-integration-4.12.0\AndroidManifest.xml:13:13-40
68
69        <provider
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
70            android:name="androidx.startup.InitializationProvider"
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
71            android:authorities="com.autoflowmobile.androidx-startup"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
72            android:exported="false" >
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
73            <meta-data
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.emoji2.text.EmojiCompatInitializer"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
75                android:value="androidx.startup" />
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
78                android:value="androidx.startup" />
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
81                android:value="androidx.startup" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
82        </provider>
83
84        <receiver
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
85            android:name="androidx.profileinstaller.ProfileInstallReceiver"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
86            android:directBootAware="false"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
87            android:enabled="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
88            android:exported="true"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
89            android:permission="android.permission.DUMP" >
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
91                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
94                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
97                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
100                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
101            </intent-filter>
102        </receiver>
103
104        <meta-data
104-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
105            android:name="com.facebook.soloader.enabled"
105-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
106            android:value="false" />
106-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
107    </application>
108
109</manifest>
