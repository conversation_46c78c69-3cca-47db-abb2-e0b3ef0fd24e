1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autoflowmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <!--
13    This manifest file is used only by <PERSON>radle to configure debug-only capabilities
14    for React Native Apps.
15    -->
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
16-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
17    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
17-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
17-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
18
19    <permission
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-24:19
26        android:name="com.autoflowmobile.MainApplication"
26-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:7-38
27        android:allowBackup="false"
27-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:7-34
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
29        android:debuggable="true"
30        android:extractNativeLibs="true"
31        android:icon="@mipmap/ic_launcher"
31-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:8:7-41
32        android:label="@string/app_name"
32-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:7:7-39
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:7-52
34        android:theme="@style/AppTheme"
34-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:11:7-38
35        android:usesCleartextTraffic="true" >
35-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
36        <activity
36-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:7-23:18
37            android:name="com.autoflowmobile.MainActivity"
37-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:9-37
38            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
38-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:9-118
39            android:exported="true"
39-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:9-32
40            android:label="@string/app_name"
40-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:9-41
41            android:launchMode="singleTask"
41-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:9-40
42            android:windowSoftInputMode="adjustResize" >
42-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:9-51
43            <intent-filter>
43-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:9-22:25
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:13-65
44-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:21-62
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:13-73
46-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:23-70
47            </intent-filter>
48        </activity>
49        <activity
49-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
50            android:name="com.facebook.react.devsupport.DevSettingsActivity"
50-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
51            android:exported="false" />
51-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.autoflowmobile.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
63        </provider>
64
65        <meta-data
65-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
66            android:name="com.facebook.soloader.enabled"
66-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
67            android:value="false" />
67-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
68    </application>
69
70</manifest>
