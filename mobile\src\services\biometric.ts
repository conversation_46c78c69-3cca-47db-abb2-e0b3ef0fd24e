import * as Keychain from 'react-native-keychain';
import { simpleStorage } from './storage';

export interface BiometricConfig {
  title: string;
  subtitle?: string;
  description?: string;
  fallbackLabel?: string;
  cancelLabel?: string;
  color?: string;
}

export interface BiometricCredentials {
  email: string;
  userId: string;
}

export class BiometricService {
  private static readonly BIOMETRIC_KEY = 'autoflow_biometric_credentials';
  private static readonly BIOMETRIC_ENABLED_KEY = 'autoflow_biometric_enabled';
  private static readonly DEMO_MODE = false; // Set to false to use real biometric hardware

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    try {
      console.log('BiometricService: Checking availability using Keychain...');

      // Check if biometric authentication is supported using Keychain
      const biometryType = await Keychain.getSupportedBiometryType();
      console.log('BiometricService: Keychain biometry type:', biometryType);

      const isSupported = biometryType !== null && biometryType !== undefined;
      console.log('BiometricService: Biometric supported:', isSupported);

      return isSupported;
    } catch (error) {
      console.log('BiometricService: Error checking availability:', error);
      return false;
    }
  }

  // Get supported biometric type
  async getBiometricType(): Promise<string | null> {
    try {
      console.log('BiometricService: Getting biometric type using Keychain...');

      const biometryType = await Keychain.getSupportedBiometryType();
      console.log('BiometricService: Keychain biometry type:', biometryType);

      if (biometryType) {
        // Convert Keychain biometry types to user-friendly names
        switch (biometryType) {
          case Keychain.BIOMETRY_TYPE.TOUCH_ID:
            return 'Touch ID';
          case Keychain.BIOMETRY_TYPE.FACE_ID:
            return 'Face ID';
          case Keychain.BIOMETRY_TYPE.FINGERPRINT:
            return 'Fingerprint';
          case Keychain.BIOMETRY_TYPE.FACE:
            return 'Face Recognition';
          case Keychain.BIOMETRY_TYPE.IRIS:
            return 'Iris Recognition';
          default:
            return 'Biometric';
        }
      }

      return null;
    } catch (error) {
      console.log('BiometricService: Error getting biometric type:', error);
      return null;
    }
  }

  // Check if user has enabled biometric authentication
  async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await simpleStorage.getItem(BiometricService.BIOMETRIC_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.log('Error checking biometric enabled status:', error);
      return false;
    }
  }

  // Enable biometric authentication
  async enableBiometric(email: string, userId: string): Promise<boolean> {
    try {
      console.log('BiometricService: Enabling biometric for', email);

      if (BiometricService.DEMO_MODE) {
        console.log('BiometricService: Running in demo mode - simulating biometric setup');

        // Simulate a brief delay for setup
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Store credentials in simple storage for demo
        const credentials: BiometricCredentials = {
          email,
          userId,
        };

        await simpleStorage.setItem(BiometricService.BIOMETRIC_KEY, JSON.stringify(credentials));
        await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'true');

        console.log('BiometricService: Demo biometric setup completed successfully');
        return true;
      }

      // Production mode - use actual biometric hardware
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        throw new Error('Biometric authentication is not available on this device');
      }

      // Store credentials securely with biometric authentication using Keychain
      const credentials: BiometricCredentials = {
        email,
        userId,
      };

      // Use Keychain to store credentials with biometric protection
      await Keychain.setInternetCredentials(
        BiometricService.BIOMETRIC_KEY,
        email,
        JSON.stringify(credentials),
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY,
          authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
          showModal: true,
          kLocalizedFallbackTitle: 'Use Password',
        }
      );

      // Mark biometric as enabled
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'true');

      console.log('BiometricService: Biometric authentication enabled successfully');
      return true;
    } catch (error) {
      console.log('Error enabling biometric:', error);
      throw error;
    }
  }

  // Disable biometric authentication
  async disableBiometric(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(BiometricService.BIOMETRIC_KEY);
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'false');
    } catch (error) {
      console.log('Error disabling biometric:', error);
      throw error;
    }
  }

  // This method is no longer needed as Keychain handles authentication internally

  // Get stored credentials after biometric authentication
  async getBiometricCredentials(): Promise<BiometricCredentials | null> {
    try {
      console.log('BiometricService: Getting biometric credentials');

      if (BiometricService.DEMO_MODE) {
        console.log('BiometricService: Demo mode - simulating biometric authentication');

        // Simulate biometric authentication delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Get stored credentials from simple storage
        const credentialsStr = await simpleStorage.getItem(BiometricService.BIOMETRIC_KEY);
        if (credentialsStr) {
          console.log('BiometricService: Demo credentials found');
          return JSON.parse(credentialsStr) as BiometricCredentials;
        }

        console.log('BiometricService: No demo credentials found');
        return null;
      }

      // Production mode - use actual biometric authentication via Keychain
      // Keychain will automatically prompt for biometric authentication
      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);

      if (credentials && credentials.password) {
        return JSON.parse(credentials.password) as BiometricCredentials;
      }

      return null;
    } catch (error) {
      console.log('BiometricService: Error getting biometric credentials:', error);
      throw error;
    }
  }



  // Check if biometric credentials exist
  async hasBiometricCredentials(): Promise<boolean> {
    try {
      if (BiometricService.DEMO_MODE) {
        const credentialsStr = await simpleStorage.getItem(BiometricService.BIOMETRIC_KEY);
        return credentialsStr !== null;
      }

      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);
      return credentials && credentials.password !== false;
    } catch (error) {
      console.log('Error checking biometric credentials:', error);
      return false;
    }
  }

  // Get biometric prompt message based on type
  async getBiometricPromptMessage(): Promise<string> {
    const biometricType = await this.getBiometricType();
    switch (biometricType) {
      case 'Face ID':
        return 'Use Face ID to login to AutoFlow';
      case 'Touch ID':
        return 'Use Touch ID to login to AutoFlow';
      case 'Fingerprint':
        return 'Use your fingerprint to login to AutoFlow';
      default:
        return 'Use biometric authentication to login to AutoFlow';
    }
  }
}

export const biometricService = new BiometricService();
export default biometricService;
