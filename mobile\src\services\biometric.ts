import * as Keychain from 'react-native-keychain';
import { simpleStorage } from './storage';

export interface BiometricConfig {
  title: string;
  subtitle?: string;
  description?: string;
  fallbackLabel?: string;
  cancelLabel?: string;
  color?: string;
}

export interface BiometricCredentials {
  email: string;
  userId: string;
}

export class BiometricService {
  private static readonly BIOMETRIC_KEY = 'autoflow_biometric_credentials';
  private static readonly BIOMETRIC_ENABLED_KEY = 'autoflow_biometric_enabled';

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    try {
      console.log('BiometricService: Checking real biometric availability...');

      // Check if biometric authentication is supported using Keychain
      const biometryType = await Keychain.getSupportedBiometryType();
      console.log('BiometricService: Keychain biometry type:', biometryType);

      const isSupported = biometryType !== null && biometryType !== undefined;
      console.log('BiometricService: Real biometric supported:', isSupported);

      return isSupported;
    } catch (error) {
      console.log('BiometricService: Error checking availability:', error);
      return false;
    }
  }

  // Get supported biometric type
  async getBiometricType(): Promise<string | null> {
    try {
      console.log('BiometricService: Getting real biometric type...');

      const biometryType = await Keychain.getSupportedBiometryType();
      console.log('BiometricService: Keychain biometry type:', biometryType);

      if (biometryType) {
        // Convert Keychain biometry types to user-friendly names
        switch (biometryType) {
          case Keychain.BIOMETRY_TYPE.TOUCH_ID:
            return 'Touch ID';
          case Keychain.BIOMETRY_TYPE.FACE_ID:
            return 'Face ID';
          case Keychain.BIOMETRY_TYPE.FINGERPRINT:
            return 'Fingerprint';
          case Keychain.BIOMETRY_TYPE.FACE:
            return 'Face Recognition';
          case Keychain.BIOMETRY_TYPE.IRIS:
            return 'Iris Recognition';
          default:
            return 'Biometric';
        }
      }

      return null;
    } catch (error) {
      console.log('BiometricService: Error getting biometric type:', error);
      return null;
    }
  }

  // Check if user has enabled biometric authentication
  async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await simpleStorage.getItem(BiometricService.BIOMETRIC_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.log('Error checking biometric enabled status:', error);
      return false;
    }
  }

  // Enable biometric authentication
  async enableBiometric(email: string, userId: string): Promise<boolean> {
    try {
      console.log('BiometricService: Enabling real biometric authentication for', email);

      // Check if biometric authentication is available
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        throw new Error('Biometric authentication is not available on this device');
      }

      console.log('BiometricService: Setting up biometric authentication...');

      // Store credentials with biometric protection using Keychain
      const credentials: BiometricCredentials = {
        email,
        userId,
      };

      // This will trigger the real Android biometric prompt for setup
      await Keychain.setInternetCredentials(
        BiometricService.BIOMETRIC_KEY,
        email,
        JSON.stringify(credentials),
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY,
          authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
          showModal: true,
          kLocalizedFallbackTitle: 'Use Password',
        }
      );

      // Mark biometric as enabled
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'true');

      console.log('BiometricService: Biometric authentication enabled successfully');
      return true;
    } catch (error) {
      console.log('BiometricService: Error enabling biometric:', error);
      throw error;
    }
  }

  // Disable biometric authentication
  async disableBiometric(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(BiometricService.BIOMETRIC_KEY);
      await simpleStorage.setItem(BiometricService.BIOMETRIC_ENABLED_KEY, 'false');
    } catch (error) {
      console.log('Error disabling biometric:', error);
      throw error;
    }
  }

  // This method is no longer needed as Keychain handles authentication internally

  // Get stored credentials after biometric authentication
  async getBiometricCredentials(): Promise<BiometricCredentials | null> {
    try {
      console.log('BiometricService: Getting biometric credentials with real authentication');

      // This will trigger the real Android biometric prompt for authentication
      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);

      if (credentials && credentials.password) {
        console.log('BiometricService: Biometric authentication successful');
        return JSON.parse(credentials.password) as BiometricCredentials;
      }

      console.log('BiometricService: No biometric credentials found');
      return null;
    } catch (error) {
      console.log('BiometricService: Biometric authentication failed:', error);
      throw error;
    }
  }



  // Check if biometric credentials exist
  async hasBiometricCredentials(): Promise<boolean> {
    try {
      console.log('BiometricService: Checking if biometric credentials exist');

      const credentials = await Keychain.getInternetCredentials(BiometricService.BIOMETRIC_KEY);
      const hasCredentials = credentials && credentials.password !== false;

      console.log('BiometricService: Has biometric credentials:', hasCredentials);
      return hasCredentials;
    } catch (error) {
      console.log('BiometricService: Error checking biometric credentials:', error);
      return false;
    }
  }

  // Get biometric prompt message based on type
  async getBiometricPromptMessage(): Promise<string> {
    const biometricType = await this.getBiometricType();
    switch (biometricType) {
      case 'Face ID':
        return 'Use Face ID to login to AutoFlow';
      case 'Touch ID':
        return 'Use Touch ID to login to AutoFlow';
      case 'Fingerprint':
        return 'Use your fingerprint to login to AutoFlow';
      default:
        return 'Use biometric authentication to login to AutoFlow';
    }
  }
}

export const biometricService = new BiometricService();
export default biometricService;
