{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,396,512,592,656,750,818,877,972,1036,1095,1162,1225,1279,1394,1452,1514,1568,1639,1771,1855,1935,2039,2115,2191,2275,2342,2408,2478,2556,2639,2709,2785,2863,2934,3020,3103,3196,3289,3362,3434,3528,3582,3649,3733,3821,3885,3950,4014,4116,4213,4309,4406", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "215,295,391,507,587,651,745,813,872,967,1031,1090,1157,1220,1274,1389,1447,1509,1563,1634,1766,1850,1930,2034,2110,2186,2270,2337,2403,2473,2551,2634,2704,2780,2858,2929,3015,3098,3191,3284,3357,3429,3523,3577,3644,3728,3816,3880,3945,4009,4111,4208,4304,4401,4481"}, "to": {"startLines": "2,33,41,42,43,45,46,47,50,52,53,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2978,3785,3881,3997,4158,4222,4316,4535,4662,4757,4821,4880,4947,5010,5064,5179,5237,5299,5353,5424,5624,5708,5788,5892,5968,6044,6128,6195,6261,6331,6409,6492,6562,6638,6716,6787,6873,6956,7049,7142,7215,7287,7381,7435,7502,7586,7674,7738,7803,7867,7969,8066,8162,8259", "endLines": "5,33,41,42,43,45,46,47,50,52,53,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "265,3053,3876,3992,4072,4217,4311,4379,4589,4752,4816,4875,4942,5005,5059,5174,5232,5294,5348,5419,5551,5703,5783,5887,5963,6039,6123,6190,6256,6326,6404,6487,6557,6633,6711,6782,6868,6951,7044,7137,7210,7282,7376,7430,7497,7581,7669,7733,7798,7862,7964,8061,8157,8254,8334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "44,48,49,51,64,100,101", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4077,4384,4453,4594,5556,8423,8498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "4153,4448,4530,4657,5619,8493,8568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "34,35,36,37,38,39,40,102", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3058,3159,3261,3364,3468,3569,3674,8573", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3154,3256,3359,3463,3564,3669,3780,8669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,380,481,591,679,786,900,982,1060,1151,1244,1338,1437,1537,1630,1725,1819,1910,2002,2087,2192,2298,2398,2507,2612,2714,2872,8339", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "375,476,586,674,781,895,977,1055,1146,1239,1333,1432,1532,1625,1720,1814,1905,1997,2082,2187,2293,2393,2502,2607,2709,2867,2973,8418"}}]}]}