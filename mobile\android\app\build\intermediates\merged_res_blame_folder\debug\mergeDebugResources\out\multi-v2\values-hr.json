{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-35:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,232,322,406,480,549,630,697,768,849,932,1016,1105,1177,1263,1346,1422,1502,1584,1663,1741,1817,1907,1980,2059,2137", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,227,317,401,475,544,625,692,763,844,927,1011,1100,1172,1258,1341,1417,1497,1579,1658,1736,1812,1902,1975,2054,2132,2213"}, "to": {"startLines": "34,40,41,45,48,50,51,53,67,68,69,107,108,109,110,112,113,114,115,116,117,118,119,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,3586,3689,4089,4337,4479,4548,4692,5742,5813,5894,8950,9034,9123,9195,9366,9449,9525,9605,9687,9766,9844,9920,10111,10184,10263,10341", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3150,3684,3774,4168,4406,4543,4624,4754,5808,5889,5972,9029,9118,9190,9276,9444,9520,9600,9682,9761,9839,9915,10005,10179,10258,10336,10417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\603752beef54a2060376812c2cf342db\\transformed\\material-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2903,2959,3014,3080,3154,3232,3320,3392,3469,3549,3623,3716,3789,3881,3977,4051,4127,4223,4275,4342,4429,4516,4578,4642,4705,4811,4912,5009,5113,5173,5232", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2898,2954,3009,3075,3149,3227,3315,3387,3464,3544,3618,3711,3784,3876,3972,4046,4122,4218,4270,4337,4424,4511,4573,4637,4700,4806,4907,5004,5108,5168,5227,5307"}, "to": {"startLines": "2,35,36,37,38,39,42,43,44,46,47,49,52,54,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3233,3311,3396,3493,3779,3875,4005,4173,4241,4411,4629,4759,4867,4927,4993,5049,5120,5180,5234,5360,5417,5479,5533,5608,5977,6062,6143,6280,6364,6450,6541,6597,6652,6718,6792,6870,6958,7030,7107,7187,7261,7354,7427,7519,7615,7689,7765,7861,7913,7980,8067,8154,8216,8280,8343,8449,8550,8647,8751,8811,8870", "endLines": "6,35,36,37,38,39,42,43,44,46,47,49,52,54,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "365,3228,3306,3391,3488,3581,3870,4000,4084,4236,4332,4474,4687,4862,4922,4988,5044,5115,5175,5229,5355,5412,5474,5528,5603,5737,6057,6138,6275,6359,6445,6536,6592,6647,6713,6787,6865,6953,7025,7102,7182,7256,7349,7422,7514,7610,7684,7760,7856,7908,7975,8062,8149,8211,8275,8338,8444,8545,8642,8746,8806,8865,8945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,9281", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,9361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "10010", "endColumns": "100", "endOffsets": "10106"}}]}]}