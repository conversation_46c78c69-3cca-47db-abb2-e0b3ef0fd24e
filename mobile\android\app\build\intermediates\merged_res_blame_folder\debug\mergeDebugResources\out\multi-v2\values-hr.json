{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,430,525,632,718,822,941,1026,1108,1199,1292,1387,1481,1581,1674,1769,1864,1955,2046,2132,2236,2348,2449,2554,2668,2770,2939,9363", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "425,520,627,713,817,936,1021,1103,1194,1287,1382,1476,1576,1669,1764,1859,1950,2041,2127,2231,2343,2444,2549,2663,2765,2934,3031,9443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,360,456,586,670,738,834,902,965,1073,1139,1195,1266,1326,1380,1506,1563,1625,1679,1754,1888,1973,2054,2161,2245,2331,2422,2489,2555,2629,2707,2795,2867,2944,3024,3098,3191,3264,3356,3452,3526,3602,3698,3750,3817,3904,3991,4053,4117,4180,4286,4387,4484,4588", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "270,355,451,581,665,733,829,897,960,1068,1134,1190,1261,1321,1375,1501,1558,1620,1674,1749,1883,1968,2049,2156,2240,2326,2417,2484,2550,2624,2702,2790,2862,2939,3019,3093,3186,3259,3351,3447,3521,3597,3693,3745,3812,3899,3986,4048,4112,4175,4281,4382,4479,4583,4663"}, "to": {"startLines": "2,35,45,46,47,49,50,52,55,57,58,59,60,61,62,63,64,65,66,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3110,4114,4210,4340,4508,4576,4746,4964,5094,5202,5268,5324,5395,5455,5509,5635,5692,5754,5808,5883,6252,6337,6418,6525,6609,6695,6786,6853,6919,6993,7071,7159,7231,7308,7388,7462,7555,7628,7720,7816,7890,7966,8062,8114,8181,8268,8355,8417,8481,8544,8650,8751,8848,8952", "endLines": "6,35,45,46,47,49,50,52,55,57,58,59,60,61,62,63,64,65,66,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "320,3190,4205,4335,4419,4571,4667,4809,5022,5197,5263,5319,5390,5450,5504,5630,5687,5749,5803,5878,6012,6332,6413,6520,6604,6690,6781,6848,6914,6988,7066,7154,7226,7303,7383,7457,7550,7623,7715,7811,7885,7961,8057,8109,8176,8263,8350,8412,8476,8539,8645,8746,8843,8947,9027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "36,37,38,39,40,41,42,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3293,3400,3497,3596,3700,3804,10092", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3288,3395,3492,3591,3695,3799,3916,10188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,232,322,406,480,549,630,697,768,849,932,1016,1105,1177,1263,1346,1422,1502,1584,1663,1741,1817,1907,1980,2059,2137", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,227,317,401,475,544,625,692,763,844,927,1011,1100,1172,1258,1341,1417,1497,1579,1658,1736,1812,1902,1975,2054,2132,2213"}, "to": {"startLines": "34,43,44,48,51,53,54,56,69,70,71,106,107,108,109,111,112,113,114,115,116,117,118,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,3921,4024,4424,4672,4814,4883,5027,6017,6088,6169,9032,9116,9205,9277,9448,9531,9607,9687,9769,9848,9926,10002,10193,10266,10345,10423", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3105,4019,4109,4503,4741,4878,4959,5089,6083,6164,6247,9111,9200,9272,9358,9526,9602,9682,9764,9843,9921,9997,10087,10261,10340,10418,10499"}}]}]}