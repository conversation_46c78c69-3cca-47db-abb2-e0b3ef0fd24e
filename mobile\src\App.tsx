import React, { useState, useEffect } from 'react';
import { View, StyleSheet, StatusBar, TouchableOpacity, Text, Alert } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from './constants/theme';
import { biometricService } from './services/biometric';
import { simpleStorage } from './services/storage';

// Import components
import Header from './components/Header';
import MobileMenu from './components/MobileMenu';

// Import screens
import HomeScreen from './screens/HomeScreen';
import LoginScreen from './screens/LoginScreen';
import PartsScreen from './screens/PartsScreen';
import ProfileScreen from './screens/ProfileScreen';
import { ShopScreen } from './screens/shop/ShopScreen';
import { SearchScreen } from './screens/main/SearchScreen';

type Screen = 'Home' | 'Shop' | 'Search' | 'Profile';

const App: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('Home');
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showBiometricPrompt, setShowBiometricPrompt] = useState(false);

  // Check authentication status on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);

      // Check if user was previously logged in
      const wasLoggedIn = await simpleStorage.getItem('user_logged_in');

      if (wasLoggedIn === 'true') {
        // Check if biometric is available and enabled
        const biometricAvailable = await biometricService.isBiometricAvailable();
        const biometricEnabled = await biometricService.isBiometricEnabled();

        if (biometricAvailable && biometricEnabled) {
          setShowBiometricPrompt(true);
        } else {
          // User was logged in but no biometric, go to login screen
          setIsAuthenticated(false);
        }
      } else {
        // No previous login, show login screen
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = () => {
    setIsAuthenticated(true);
    setShowBiometricPrompt(false);
  };

  const handleBiometricPrompt = async () => {
    try {
      const credentials = await biometricService.getBiometricCredentials();
      if (credentials) {
        setIsAuthenticated(true);
        setShowBiometricPrompt(false);
      }
    } catch (error: any) {
      console.log('Biometric login error:', error);

      if (error.message?.includes('cancelled') || error.message?.includes('canceled')) {
        // User cancelled, show login screen
        setShowBiometricPrompt(false);
        setIsAuthenticated(false);
        return;
      }

      Alert.alert(
        'Authentication Failed',
        'Biometric authentication failed. Please use your password to login.',
        [
          {
            text: 'Use Password',
            onPress: () => {
              setShowBiometricPrompt(false);
              setIsAuthenticated(false);
            },
          },
        ]
      );
    }
  };

  const handleLogout = async () => {
    try {
      await simpleStorage.removeItem('user_logged_in');
      await simpleStorage.removeItem('user_email');
      setIsAuthenticated(false);
      setCurrentScreen('Home');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    { id: 'home', label: 'Home', onPress: () => setCurrentScreen('Home') },
    { id: 'shop', label: 'Shop Parts', onPress: () => setCurrentScreen('Shop') },
    { id: 'search', label: 'Search', onPress: () => setCurrentScreen('Search') },
    { id: 'profile', label: 'Profile', onPress: () => setCurrentScreen('Profile') },
    { id: 'categories', label: 'Categories', onPress: () => {} },
    { id: 'brands', label: 'Brands', onPress: () => {} },
    { id: 'about', label: 'About Us', onPress: () => {} },
    { id: 'contact', label: 'Contact', onPress: () => {} },
    { id: 'logout', label: 'Logout', onPress: handleLogout },
  ];

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    setCurrentScreen('Search');
  };

  // Show loading screen
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <Text style={styles.loadingText}>Loading AutoFlow...</Text>
      </View>
    );
  }

  // Show biometric prompt if available
  if (showBiometricPrompt) {
    return (
      <View style={[styles.container, styles.biometricContainer]}>
        <View style={styles.biometricPrompt}>
          <Text style={styles.biometricTitle}>Welcome Back!</Text>
          <Text style={styles.biometricSubtitle}>Use your biometric authentication to continue</Text>

          <TouchableOpacity style={styles.biometricButton} onPress={handleBiometricPrompt}>
            <Text style={styles.biometricIcon}>👆</Text>
            <Text style={styles.biometricButtonText}>Authenticate</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.passwordButton}
            onPress={() => {
              setShowBiometricPrompt(false);
              setIsAuthenticated(false);
            }}
          >
            <Text style={styles.passwordButtonText}>Use Password Instead</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <LoginScreen
        onNavigate={(screen) => {
          if (screen === 'Home') {
            handleLogin();
          }
        }}
      />
    );
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case 'Home':
        return <HomeScreen onNavigate={setCurrentScreen} />;
      case 'Shop':
        return <ShopScreen />;
      case 'Search':
        return <SearchScreen />;
      case 'Profile':
        return <ProfileScreen onNavigate={setCurrentScreen} />;
      default:
        return <HomeScreen onNavigate={setCurrentScreen} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a2332" />

      {/* Header - Only show for non-Home screens */}
      {currentScreen !== 'Home' && (
        <Header
          onMenuPress={() => setIsMenuVisible(true)}
          onSearch={handleSearch}
        />
      )}

      {/* Screen Content */}
      <View style={styles.content}>
        {renderScreen()}
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Home' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Home')}
        >
          <Text style={styles.navIcon}>🏠</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Home' && styles.activeNavButtonText]}>
            Home
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Shop' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Shop')}
        >
          <Text style={styles.navIcon}>🏪</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Shop' && styles.activeNavButtonText]}>
            Shop
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Search' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Search')}
        >
          <Text style={styles.navIcon}>🔍</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Search' && styles.activeNavButtonText]}>
            Search
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Profile' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Profile')}
        >
          <Text style={styles.navIcon}>👤</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Profile' && styles.activeNavButtonText]}>
            Profile
          </Text>
        </TouchableOpacity>
      </View>

      {/* Mobile Menu */}
      <MobileMenu
        isVisible={isMenuVisible}
        onClose={() => setIsMenuVisible(false)}
        menuItems={menuItems}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    flex: 1,
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: COLORS.SURFACE,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingBottom: 5,
    paddingTop: 5,
    height: 60,
  },
  navButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  activeNavButton: {
    backgroundColor: 'transparent',
  },
  navIcon: {
    fontSize: 20,
    marginBottom: 2,
  },
  navButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.TEXT_SECONDARY,
  },
  activeNavButtonText: {
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.TEXT_SECONDARY,
  },
  biometricContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  biometricPrompt: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.xl,
    borderRadius: 16,
    alignItems: 'center',
    width: '100%',
    maxWidth: 300,
  },
  biometricTitle: {
    fontSize: TYPOGRAPHY.sizes.xxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.sm,
  },
  biometricSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  biometricButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: SPACING.lg,
    minWidth: 200,
  },
  biometricIcon: {
    fontSize: 32,
    marginBottom: SPACING.sm,
  },
  biometricButtonText: {
    color: COLORS.WHITE,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  passwordButton: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  passwordButtonText: {
    color: COLORS.PRIMARY,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
});

export default App;
