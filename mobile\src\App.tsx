import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

// Import screens
import HomeScreen from './screens/HomeScreen';
import LoginScreen from './screens/LoginScreen';
import PartsScreen from './screens/PartsScreen';
import ProfileScreen from './screens/ProfileScreen';

type Screen = 'Home' | 'Login' | 'Parts' | 'Profile';

const App: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('Home');

  const renderScreen = () => {
    switch (currentScreen) {
      case 'Home':
        return <HomeScreen onNavigate={setCurrentScreen} />;
      case 'Login':
        return <LoginScreen onNavigate={setCurrentScreen} />;
      case 'Parts':
        return <PartsScreen onNavigate={setCurrentScreen} />;
      case 'Profile':
        return <ProfileScreen onNavigate={setCurrentScreen} />;
      default:
        return <HomeScreen onNavigate={setCurrentScreen} />;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>AutoFlow Mobile</Text>
      </View>

      {/* Screen Content */}
      <View style={styles.content}>
        {renderScreen()}
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Home' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Home')}
        >
          <Text style={[styles.navButtonText, currentScreen === 'Home' && styles.activeNavButtonText]}>
            🏠 Home
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Parts' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Parts')}
        >
          <Text style={[styles.navButtonText, currentScreen === 'Parts' && styles.activeNavButtonText]}>
            🔧 Parts
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Login' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Login')}
        >
          <Text style={[styles.navButtonText, currentScreen === 'Login' && styles.activeNavButtonText]}>
            🔐 Login
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Profile' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Profile')}
        >
          <Text style={[styles.navButtonText, currentScreen === 'Profile' && styles.activeNavButtonText]}>
            👤 Profile
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 15,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#f8f8f8',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navButton: {
    flex: 1,
    padding: 15,
    alignItems: 'center',
  },
  activeNavButton: {
    backgroundColor: '#4CAF50',
  },
  navButtonText: {
    fontSize: 12,
    color: '#666',
  },
  activeNavButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default App;
