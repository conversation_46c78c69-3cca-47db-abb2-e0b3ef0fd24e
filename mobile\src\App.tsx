import React, { useState, useEffect } from 'react';
import { View, StyleSheet, StatusBar, TouchableOpacity, Text } from 'react-native';
import { COLORS, TYPOGRAPHY } from './constants/theme';
import { authService } from './services/auth';
import { User } from './types';

// Import components
import Header from './components/Header';
import MobileMenu from './components/MobileMenu';

// Import screens
import HomeScreen from './screens/HomeScreen';
import LoginScreen from './screens/LoginScreen';
import PartsScreen from './screens/PartsScreen';
import ProfileScreen from './screens/ProfileScreen';
import { ShopScreen } from './screens/shop/ShopScreen';
import { SearchScreen } from './screens/main/SearchScreen';
import BiometricLoginScreen from './screens/auth/BiometricLoginScreen';

type Screen = 'Home' | 'Shop' | 'Search' | 'Profile';
type AuthScreen = 'Login' | 'BiometricLogin' | 'Register';

const App: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('Home');
  const [authScreen, setAuthScreen] = useState<AuthScreen | null>('Login');
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check authentication status on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);

      // Check if user is already authenticated
      const currentUser = await authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        setAuthScreen(null);
      } else {
        // Check if biometric login is available
        const isBiometricAvailable = await authService.isBiometricLoginAvailable();
        if (isBiometricAvailable) {
          setAuthScreen('BiometricLogin');
        } else {
          setAuthScreen('Login');
        }
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setAuthScreen('Login');
    } finally {
      setIsLoading(false);
    }
  };

  const menuItems = [
    { id: 'home', label: 'Home', onPress: () => setCurrentScreen('Home') },
    { id: 'shop', label: 'Shop Parts', onPress: () => setCurrentScreen('Shop') },
    { id: 'search', label: 'Search', onPress: () => setCurrentScreen('Search') },
    { id: 'profile', label: 'Profile', onPress: () => setCurrentScreen('Profile') },
    { id: 'categories', label: 'Categories', onPress: () => {} },
    { id: 'brands', label: 'Brands', onPress: () => {} },
    { id: 'about', label: 'About Us', onPress: () => {} },
    { id: 'contact', label: 'Contact', onPress: () => {} },
    { id: 'logout', label: 'Logout', onPress: handleLogout },
  ];

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    setCurrentScreen('Search');
  };

  const handleLoginSuccess = (loggedInUser: User) => {
    setUser(loggedInUser);
    setAuthScreen(null);
  };

  const handleLogout = async () => {
    try {
      await authService.signOut();
      setUser(null);
      setAuthScreen('Login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading screen
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // Show authentication screens
  if (authScreen) {
    switch (authScreen) {
      case 'BiometricLogin':
        return (
          <BiometricLoginScreen
            onLoginSuccess={handleLoginSuccess}
            onFallbackToPassword={() => setAuthScreen('Login')}
            onCancel={() => setAuthScreen('Login')}
          />
        );
      case 'Login':
        return (
          <LoginScreen
            onNavigate={(screen) => {
              if (screen === 'Home') {
                // This means login was successful
                checkAuthStatus();
              }
            }}
          />
        );
      default:
        return (
          <LoginScreen
            onNavigate={(screen) => {
              if (screen === 'Home') {
                checkAuthStatus();
              }
            }}
          />
        );
    }
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case 'Home':
        return <HomeScreen onNavigate={setCurrentScreen} />;
      case 'Shop':
        return <ShopScreen />;
      case 'Search':
        return <SearchScreen />;
      case 'Profile':
        return <ProfileScreen onNavigate={setCurrentScreen} />;
      default:
        return <HomeScreen onNavigate={setCurrentScreen} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a2332" />

      {/* Header - Only show for non-Home screens */}
      {currentScreen !== 'Home' && (
        <Header
          onMenuPress={() => setIsMenuVisible(true)}
          onSearch={handleSearch}
        />
      )}

      {/* Screen Content */}
      <View style={styles.content}>
        {renderScreen()}
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Home' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Home')}
        >
          <Text style={styles.navIcon}>🏠</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Home' && styles.activeNavButtonText]}>
            Home
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Shop' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Shop')}
        >
          <Text style={styles.navIcon}>🏪</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Shop' && styles.activeNavButtonText]}>
            Shop
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Search' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Search')}
        >
          <Text style={styles.navIcon}>🔍</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Search' && styles.activeNavButtonText]}>
            Search
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentScreen === 'Profile' && styles.activeNavButton]}
          onPress={() => setCurrentScreen('Profile')}
        >
          <Text style={styles.navIcon}>👤</Text>
          <Text style={[styles.navButtonText, currentScreen === 'Profile' && styles.activeNavButtonText]}>
            Profile
          </Text>
        </TouchableOpacity>
      </View>

      {/* Mobile Menu */}
      <MobileMenu
        isVisible={isMenuVisible}
        onClose={() => setIsMenuVisible(false)}
        menuItems={menuItems}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    flex: 1,
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: COLORS.SURFACE,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingBottom: 5,
    paddingTop: 5,
    height: 60,
  },
  navButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  activeNavButton: {
    backgroundColor: 'transparent',
  },
  navIcon: {
    fontSize: 20,
    marginBottom: 2,
  },
  navButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.TEXT_SECONDARY,
  },
  activeNavButtonText: {
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.TEXT_SECONDARY,
  },
});

export default App;
