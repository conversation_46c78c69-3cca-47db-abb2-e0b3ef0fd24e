import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

type Screen = 'Home' | 'Login' | 'Parts' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

// Mock parts data
const mockParts = [
  {
    id: 1,
    title: 'Volkswagen (VW) Audi Brake Pad Set 1K0698151',
    price: 'KSh 4,500',
    condition: 'New',
    stock: 5,
  },
  {
    id: 2,
    title: 'VW Golf Engine Oil Filter 06A115561B',
    price: 'KSh 1,200',
    condition: 'New',
    stock: 12,
  },
  {
    id: 3,
    title: 'Audi A3 Headlight Assembly 8P0941003',
    price: 'KSh 15,000',
    condition: 'Used',
    stock: 2,
  },
];

const PartsScreen: React.FC<Props> = ({ onNavigate }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>AutoFlow Parts</Text>
      <Text style={styles.subtitle}>Browse available parts</Text>
      
      <ScrollView style={styles.scrollView}>
        {mockParts.map((part) => (
          <View key={part.id} style={styles.partCard}>
            <Text style={styles.partTitle}>{part.title}</Text>
            <View style={styles.partDetails}>
              <Text style={styles.price}>{part.price}</Text>
              <Text style={styles.condition}>Condition: {part.condition}</Text>
              <Text style={styles.stock}>Stock: {part.stock}</Text>
            </View>
            <TouchableOpacity style={styles.viewButton}>
              <Text style={styles.viewButtonText}>View Details</Text>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
      
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => onNavigate('Home')}
      >
        <Text style={styles.backButtonText}>Back to Home</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  partCard: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#eee',
  },
  partTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  partDetails: {
    marginBottom: 10,
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 5,
  },
  condition: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  stock: {
    fontSize: 14,
    color: '#666',
  },
  viewButton: {
    backgroundColor: '#2196F3',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  viewButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  backButton: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PartsScreen;
