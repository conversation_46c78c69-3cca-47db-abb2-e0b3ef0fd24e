-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:1-25:12
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:1-25:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:8:1-16:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\1af68aa5549c7458847a29eaef0c53bc\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\90f619982098bc314ed9d8ded9ea203a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e523bb19e58b9e85b971b87af844b73c\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\94b3c12436ab64c8d1a9d0e1f4f4d8b6\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a99f88b96c6bc8545047d0da64238489\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\95cf405b99f234e3085aecc5a4fac12e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e1361d5de0ba0f3a84525f7d0cefa2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b41e66cbeb70cb214ad2facf9d14ef5\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc48cefa7e8e7a13719a3ad4f51dff09\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\733abbad5e9ea45dff5bb5afa3e66cdb\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
	package
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
application
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-24:19
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-24:19
MERGED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-24:19
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:7:7-39
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:7-52
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:8:7-41
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:7-34
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:7-34
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:11:7-38
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:7-38
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:7-38
activity#com.autoflowmobile.MainActivity
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:7-23:18
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:9-41
	android:launchMode
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:9-40
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:9-51
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:9-32
	android:configChanges
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:9-118
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:9-22:25
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:13-65
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:13-73
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:23-70
uses-sdk
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:flipper-integration:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\ee4ae5e64c61802912ca12749605ee0a\transformed\jetified-flipper-integration-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\1c18d9cde7d715e713225bcd2026dd44\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3f26ab7fcd04c6c1eee011cf7d8f3f88\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0a49f7cc8d44f49027d421b3baff9e88\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\3c97f369837365c09838ae0668e73b0b\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\10d5304b4b3e56d84be9b37a4119b8b3\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\f544d71f8265de8250b9577dfcb95a2f\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\63c5a3baecfee20d894995870bc5efc6\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\649e2d24b4b762c7d37bd19fca507325\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\359891d2dfda3762e28d4921d78b4469\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\619916dabfcdb5ccd33b05da32196508\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\bef241a74fcf5be1e4c958337743f1f9\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\47e8e95717439522461c8b1379e856b5\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fc56619c9d5b6633168df9f0d35c87f9\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6dbfe87e9bded10f45e3b7ae0631fcf1\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\e92b667b0e2131a945c3069c32afa30c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\274989c52e5cb29f760e01c0c8d999e4\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d5312d584cb115585334d432ad2050\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ae420931dcb655ccf2547b30442aab8\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\1af68aa5549c7458847a29eaef0c53bc\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\1af68aa5549c7458847a29eaef0c53bc\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\90f619982098bc314ed9d8ded9ea203a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\90f619982098bc314ed9d8ded9ea203a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a59746a1b06db492d2e276387a08a7e4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e523bb19e58b9e85b971b87af844b73c\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e523bb19e58b9e85b971b87af844b73c\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\94b3c12436ab64c8d1a9d0e1f4f4d8b6\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\94b3c12436ab64c8d1a9d0e1f4f4d8b6\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\837829eb1e26af0c93e67f49cb3c8db3\transformed\jetified-hermes-android-0.73.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a99f88b96c6bc8545047d0da64238489\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a99f88b96c6bc8545047d0da64238489\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\436136d14e2610b2038ba345b6898d49\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d39b91d1f2262675a3cf3bfea1f94ebf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\94285061969119739536e62248521246\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\801b9619c00ab4a19117e213f207994a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dacacc0dfa591b0c4ebf620995986bf2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47905ba69a2335e856e1567b8d6ab224\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\95cf405b99f234e3085aecc5a4fac12e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\95cf405b99f234e3085aecc5a4fac12e\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e2138332fd9b577f108442c465ad3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7d59d00214663accdb2b156610a4e6da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e1361d5de0ba0f3a84525f7d0cefa2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e1361d5de0ba0f3a84525f7d0cefa2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b41e66cbeb70cb214ad2facf9d14ef5\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b41e66cbeb70cb214ad2facf9d14ef5\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc48cefa7e8e7a13719a3ad4f51dff09\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc48cefa7e8e7a13719a3ad4f51dff09\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\733abbad5e9ea45dff5bb5afa3e66cdb\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\733abbad5e9ea45dff5bb5afa3e66cdb\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\af6a0a791711645e4e2285242794cc35\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-4\84ce586532c36fe8746840074f16844d\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
	android:name
		ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
