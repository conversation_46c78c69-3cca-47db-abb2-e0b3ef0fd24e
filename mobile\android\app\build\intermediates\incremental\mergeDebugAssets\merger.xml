<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-fast-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-fast-image\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-encrypted-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-encrypted-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-config" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-config\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>