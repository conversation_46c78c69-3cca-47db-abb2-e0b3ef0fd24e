{"name": "autoflow-mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace AutoflowMobile.xcworkspace -scheme AutoflowMobile -configuration Release -destination generic/platform=iOS -archivePath AutoflowMobile.xcarchive archive"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@supabase/supabase-js": "^2.48.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-config": "^1.5.1", "react-native-dotenv": "^3.4.11", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.14.0", "react-native-image-picker": "^7.1.0", "react-native-paper": "^5.12.3", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-svg": "^14.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.0.3", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli-platform-android": "^18.0.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.7", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}