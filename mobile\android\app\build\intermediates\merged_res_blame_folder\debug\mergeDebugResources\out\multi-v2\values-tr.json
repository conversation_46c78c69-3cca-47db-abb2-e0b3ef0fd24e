{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "35,36,37,38,39,40,41,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3108,3205,3307,3405,3502,3604,3710,9727", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3200,3302,3400,3497,3599,3705,3816,9823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,297,393,505,587,651,742,819,880,971,1034,1093,1162,1225,1279,1387,1445,1507,1561,1634,1755,1839,1930,2040,2117,2193,2280,2347,2413,2483,2560,2643,2714,2789,2867,2938,3023,3112,3207,3300,3372,3444,3540,3592,3659,3743,3833,3895,3959,4022,4116,4212,4301,4398", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "215,292,388,500,582,646,737,814,875,966,1029,1088,1157,1220,1274,1382,1440,1502,1556,1629,1750,1834,1925,2035,2112,2188,2275,2342,2408,2478,2555,2638,2709,2784,2862,2933,3018,3107,3202,3295,3367,3439,3535,3587,3654,3738,3828,3890,3954,4017,4111,4207,4296,4393,4472"}, "to": {"startLines": "2,34,43,44,45,47,48,50,53,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3031,3913,4009,4121,4280,4344,4506,4736,4868,4959,5022,5081,5150,5213,5267,5375,5433,5495,5549,5622,5967,6051,6142,6252,6329,6405,6492,6559,6625,6695,6772,6855,6926,7001,7079,7150,7235,7324,7419,7512,7584,7656,7752,7804,7871,7955,8045,8107,8171,8234,8328,8424,8513,8610", "endLines": "5,34,43,44,45,47,48,50,53,55,56,57,58,59,60,61,62,63,64,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "265,3103,4004,4116,4198,4339,4430,4578,4792,4954,5017,5076,5145,5208,5262,5370,5428,5490,5544,5617,5738,6046,6137,6247,6324,6400,6487,6554,6620,6690,6767,6850,6921,6996,7074,7145,7230,7319,7414,7507,7579,7651,7747,7799,7866,7950,8040,8102,8166,8229,8323,8419,8508,8605,8684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,216,293,364,434,517,588,655,732,812,897,977,1047,1130,1215,1290,1375,1461,1538,1612,1683,1770,1840,1919,1994", "endColumns": "68,91,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,211,288,359,429,512,583,650,727,807,892,972,1042,1125,1210,1285,1370,1456,1533,1607,1678,1765,1835,1914,1989,2066"}, "to": {"startLines": "33,42,46,49,51,52,54,67,68,69,104,105,106,107,109,110,111,112,113,114,115,116,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2962,3821,4203,4435,4583,4653,4797,5743,5810,5887,8689,8774,8854,8924,9087,9172,9247,9332,9418,9495,9569,9640,9828,9898,9977,10052", "endColumns": "68,91,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "3026,3908,4275,4501,4648,4731,4863,5805,5882,5962,8769,8849,8919,9002,9167,9242,9327,9413,9490,9564,9635,9722,9893,9972,10047,10124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,384,483,595,680,786,906,986,1061,1152,1245,1337,1431,1531,1624,1726,1821,1912,2003,2082,2189,2293,2389,2496,2599,2708,2864,9007", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "379,478,590,675,781,901,981,1056,1147,1240,1332,1426,1526,1619,1721,1816,1907,1998,2077,2184,2288,2384,2491,2594,2703,2859,2957,9082"}}]}]}