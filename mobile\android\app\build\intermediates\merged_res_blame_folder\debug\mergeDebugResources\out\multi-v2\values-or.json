{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,398,503,582,647,736,801,860,946,1010,1073,1146,1210,1264,1376,1434,1496,1550,1622,1744,1831,1917,2027,2104,2185,2276,2343,2409,2479,2556,2643,2714,2791,2860,2929,3020,3092,3181,3270,3344,3416,3502,3552,3618,3698,3782,3844,3908,3971,4071,4168,4260,4359", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "222,299,393,498,577,642,731,796,855,941,1005,1068,1141,1205,1259,1371,1429,1491,1545,1617,1739,1826,1912,2022,2099,2180,2271,2338,2404,2474,2551,2638,2709,2786,2855,2924,3015,3087,3176,3265,3339,3411,3497,3547,3613,3693,3777,3839,3903,3966,4066,4163,4255,4354,4438"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3029,3841,3935,4040,4119,4184,4273,4338,4397,4483,4547,4610,4683,4747,4801,4913,4971,5033,5087,5159,5281,5368,5454,5564,5641,5722,5813,5880,5946,6016,6093,6180,6251,6328,6397,6466,6557,6629,6718,6807,6881,6953,7039,7089,7155,7235,7319,7381,7445,7508,7608,7705,7797,7896", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "272,3101,3930,4035,4114,4179,4268,4333,4392,4478,4542,4605,4678,4742,4796,4908,4966,5028,5082,5154,5276,5363,5449,5559,5636,5717,5808,5875,5941,6011,6088,6175,6246,6323,6392,6461,6552,6624,6713,6802,6876,6948,7034,7084,7150,7230,7314,7376,7440,7503,7603,7700,7792,7891,7975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,388,498,605,691,795,915,994,1075,1166,1259,1360,1455,1555,1648,1743,1839,1930,2020,2109,2219,2323,2429,2540,2642,2760,2923,7980", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "383,493,600,686,790,910,989,1070,1161,1254,1355,1450,1550,1643,1738,1834,1925,2015,2104,2214,2318,2424,2535,2637,2755,2918,3024,8065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3106,3209,3311,3414,3519,3620,3722,8070", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3204,3306,3409,3514,3615,3717,3836,8166"}}]}]}