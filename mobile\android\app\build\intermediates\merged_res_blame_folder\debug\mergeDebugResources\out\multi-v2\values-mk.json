{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "35,36,37,38,39,40,41,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3151,3249,3351,3448,3546,3651,3754,10040", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3244,3346,3443,3541,3646,3749,3865,10136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,308,404,528,615,681,772,842,906,1009,1074,1134,1202,1265,1320,1448,1505,1567,1622,1697,1837,1924,2007,2110,2192,2277,2364,2431,2497,2570,2646,2735,2808,2884,2959,3029,3117,3192,3284,3376,3450,3524,3616,3669,3736,3819,3906,3968,4032,4095,4209,4316,4418,4529", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "221,303,399,523,610,676,767,837,901,1004,1069,1129,1197,1260,1315,1443,1500,1562,1617,1692,1832,1919,2002,2105,2187,2272,2359,2426,2492,2565,2641,2730,2803,2879,2954,3024,3112,3187,3279,3371,3445,3519,3611,3664,3731,3814,3901,3963,4027,4090,4204,4311,4413,4524,4609"}, "to": {"startLines": "2,34,44,45,46,48,49,51,54,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3069,4076,4172,4296,4466,4532,4696,4917,5049,5152,5217,5277,5345,5408,5463,5591,5648,5710,5765,5840,6206,6293,6376,6479,6561,6646,6733,6800,6866,6939,7015,7104,7177,7253,7328,7398,7486,7561,7653,7745,7819,7893,7985,8038,8105,8188,8275,8337,8401,8464,8578,8685,8787,8898", "endLines": "5,34,44,45,46,48,49,51,54,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "271,3146,4167,4291,4378,4527,4618,4761,4976,5147,5212,5272,5340,5403,5458,5586,5643,5705,5760,5835,5975,6288,6371,6474,6556,6641,6728,6795,6861,6934,7010,7099,7172,7248,7323,7393,7481,7556,7648,7740,7814,7888,7980,8033,8100,8183,8270,8332,8396,8459,8573,8680,8782,8893,8978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,339,422,495,564,646,714,781,857,940,1027,1107,1180,1264,1348,1425,1506,1588,1664,1741,1816,1909,1981,2065,2135", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "128,243,334,417,490,559,641,709,776,852,935,1022,1102,1175,1259,1343,1420,1501,1583,1659,1736,1811,1904,1976,2060,2130,2211"}, "to": {"startLines": "33,42,43,47,50,52,53,55,68,69,70,105,106,107,108,110,111,112,113,114,115,116,117,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3870,3985,4383,4623,4766,4835,4981,5980,6047,6123,8983,9070,9150,9223,9395,9479,9556,9637,9719,9795,9872,9947,10141,10213,10297,10367", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "3064,3980,4071,4461,4691,4830,4912,5044,6042,6118,6201,9065,9145,9218,9302,9474,9551,9632,9714,9790,9867,9942,10035,10208,10292,10362,10443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,488,596,682,790,909,993,1074,1165,1258,1354,1448,1548,1641,1736,1832,1923,2014,2101,2207,2313,2414,2521,2633,2737,2893,9307", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "379,483,591,677,785,904,988,1069,1160,1253,1349,1443,1543,1636,1731,1827,1918,2009,2096,2202,2308,2409,2516,2628,2732,2888,2986,9390"}}]}]}