import React, { useState } from 'react';
import { View, StyleSheet, TextInput, TouchableOpacity, Text } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';
import PartsListing from '../../components/PartsListing';

// Mock search results
const mockSearchResults = [
  {
    id: 1,
    title: 'Volkswagen (VW) Audi Brake Pad Set 1K0698151 Compatible with Golf, A3, Passat',
    price: 'KSh 4,500',
    discountedPrice: 'KSh 5,200',
    condition: 'New',
    stock: 5,
    thumbnailUrl: null,
  },
  {
    id: 2,
    title: 'VW Golf Engine Oil Filter 06A115561B Compatible with Golf MK5, MK6',
    price: 'KSh 1,200',
    condition: 'New',
    stock: 12,
    thumbnailUrl: null,
  },
  {
    id: 3,
    title: 'Audi A3 Headlight Assembly 8P0941003 Compatible with A3 8P',
    price: 'KSh 15,000',
    condition: 'Used',
    stock: 2,
    thumbnailUrl: null,
  },
];

export const SearchScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(mockSearchResults);

  const handleSearch = () => {
    if (searchQuery.trim()) {
      console.log('Searching for:', searchQuery);
      // TODO: Implement actual search functionality
      // For now, just filter mock data
      const filtered = mockSearchResults.filter(part =>
        part.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSearchResults(filtered);
    } else {
      setSearchResults(mockSearchResults);
    }
  };

  const handlePartPress = (partId: number) => {
    console.log('Part pressed:', partId);
    // TODO: Navigate to part details
  };

  const handleCallPress = (partId: number) => {
    console.log('Call pressed for part:', partId);
    // TODO: Implement call functionality
  };

  const handleWhatsAppPress = (partId: number) => {
    console.log('WhatsApp pressed for part:', partId);
    // TODO: Implement WhatsApp functionality
  };

  return (
    <View style={styles.container}>
      {/* Search Header */}
      <View style={styles.searchHeader}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search for auto parts..."
            placeholderTextColor="#999"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
          />
          <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
            <Text style={styles.searchButtonText}>Search</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Results */}
      <PartsListing
        parts={searchResults}
        onPartPress={handlePartPress}
        onCallPress={handleCallPress}
        onWhatsAppPress={handleWhatsAppPress}
        title="Search Results"
        subtitle={`${searchResults.length} parts found`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  searchHeader: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  searchContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  searchButton: {
    backgroundColor: '#e67e22',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  searchButtonText: {
    color: '#ffffff',
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});
