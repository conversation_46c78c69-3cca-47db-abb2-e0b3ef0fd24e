com.autoflowmobile.app-transition-1.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-4\0b626060f7f7db06e300bcdd57a44bfe\transformed\transition-1.2.0\res
com.autoflowmobile.app-recyclerview-1.1.0-1 C:\Users\<USER>\.gradle\caches\transforms-4\103d2c2b045a8eb03704c2446e059c8a\transformed\recyclerview-1.1.0\res
com.autoflowmobile.app-constraintlayout-2.0.1-2 C:\Users\<USER>\.gradle\caches\transforms-4\1213d02ac3e61ea06cb3172956bbd874\transformed\constraintlayout-2.0.1\res
com.autoflowmobile.app-core-runtime-2.2.0-3 C:\Users\<USER>\.gradle\caches\transforms-4\160f48f4c30f3ff757103849f5c02ea2\transformed\core-runtime-2.2.0\res
com.autoflowmobile.app-jetified-emoji2-1.3.0-4 C:\Users\<USER>\.gradle\caches\transforms-4\1fac21046692654a2cfe1da927803fa6\transformed\jetified-emoji2-1.3.0\res
com.autoflowmobile.app-lifecycle-viewmodel-2.6.2-5 C:\Users\<USER>\.gradle\caches\transforms-4\21a8d64ad6a72a6e6426f3c582fa739b\transformed\lifecycle-viewmodel-2.6.2\res
com.autoflowmobile.app-jetified-profileinstaller-1.3.1-6 C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\res
com.autoflowmobile.app-jetified-activity-1.7.0-7 C:\Users\<USER>\.gradle\caches\transforms-4\255d11e44a0bce4b21929c3922a26649\transformed\jetified-activity-1.7.0\res
com.autoflowmobile.app-sqlite-framework-2.2.0-8 C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\res
com.autoflowmobile.app-jetified-lifecycle-process-2.6.2-9 C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\res
com.autoflowmobile.app-lifecycle-runtime-2.6.2-10 C:\Users\<USER>\.gradle\caches\transforms-4\31a2d0582a1251488cd0e1b401ed8cf5\transformed\lifecycle-runtime-2.6.2\res
com.autoflowmobile.app-jetified-emoji2-views-helper-1.3.0-11 C:\Users\<USER>\.gradle\caches\transforms-4\37ceb84166e4137d8f1c57490137c6d0\transformed\jetified-emoji2-views-helper-1.3.0\res
com.autoflowmobile.app-jetified-drawee-3.1.3-12 C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\res
com.autoflowmobile.app-jetified-viewpager2-1.0.0-13 C:\Users\<USER>\.gradle\caches\transforms-4\46a0558ec0a6750943efbfdcaeff8e50\transformed\jetified-viewpager2-1.0.0\res
com.autoflowmobile.app-jetified-startup-runtime-1.1.1-14 C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\res
com.autoflowmobile.app-jetified-appcompat-resources-1.7.0-15 C:\Users\<USER>\.gradle\caches\transforms-4\4feab0228da78550494aaebafd3f6822\transformed\jetified-appcompat-resources-1.7.0\res
com.autoflowmobile.app-jetified-react-android-0.73.2-debug-16 C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\res
com.autoflowmobile.app-appcompat-1.7.0-17 C:\Users\<USER>\.gradle\caches\transforms-4\5a6ad47aa42d204c405611bf0090dcea\transformed\appcompat-1.7.0\res
com.autoflowmobile.app-coordinatorlayout-1.2.0-18 C:\Users\<USER>\.gradle\caches\transforms-4\6a9c7acc69a72ca12875e0c9a5df67e2\transformed\coordinatorlayout-1.2.0\res
com.autoflowmobile.app-fragment-1.5.4-19 C:\Users\<USER>\.gradle\caches\transforms-4\6ddbf12c59d94975ba03b174622ecb79\transformed\fragment-1.5.4\res
com.autoflowmobile.app-jetified-annotation-experimental-1.4.1-20 C:\Users\<USER>\.gradle\caches\transforms-4\8202f9fab2269747354a96d2d5132e9d\transformed\jetified-annotation-experimental-1.4.1\res
com.autoflowmobile.app-cardview-1.0.0-21 C:\Users\<USER>\.gradle\caches\transforms-4\8634fa6406a1584e482923d6d97bd58a\transformed\cardview-1.0.0\res
com.autoflowmobile.app-jetified-core-viewtree-1.0.0-22 C:\Users\<USER>\.gradle\caches\transforms-4\8b8dd7237ba1bba724e2d8bc3cbd0ba6\transformed\jetified-core-viewtree-1.0.0\res
com.autoflowmobile.app-sqlite-2.2.0-23 C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\res
com.autoflowmobile.app-jetified-flipper-0.201.0-24 C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\res
com.autoflowmobile.app-core-1.16.0-25 C:\Users\<USER>\.gradle\caches\transforms-4\9d2e7d5dda1637c878ef743014c0f3e1\transformed\core-1.16.0\res
com.autoflowmobile.app-lifecycle-livedata-2.6.2-26 C:\Users\<USER>\.gradle\caches\transforms-4\b7621a2a62a1d289a2e41e9159257f54\transformed\lifecycle-livedata-2.6.2\res
com.autoflowmobile.app-material-1.6.1-27 C:\Users\<USER>\.gradle\caches\transforms-4\b9c140c81985842af1c691a7017321e7\transformed\material-1.6.1\res
com.autoflowmobile.app-jetified-glide-4.12.0-28 C:\Users\<USER>\.gradle\caches\transforms-4\bb0da03058037bf633adbc88bb3f6a7d\transformed\jetified-glide-4.12.0\res
com.autoflowmobile.app-jetified-core-ktx-1.16.0-29 C:\Users\<USER>\.gradle\caches\transforms-4\c03b7311403e76444b368a909e69ba25\transformed\jetified-core-ktx-1.16.0\res
com.autoflowmobile.app-jetified-tracing-1.2.0-30 C:\Users\<USER>\.gradle\caches\transforms-4\c65c93c4aca5fa5fc5ee9784115f3806\transformed\jetified-tracing-1.2.0\res
com.autoflowmobile.app-jetified-autofill-1.1.0-31 C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\res
com.autoflowmobile.app-swiperefreshlayout-1.1.0-32 C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\res
com.autoflowmobile.app-jetified-savedstate-1.2.1-33 C:\Users\<USER>\.gradle\caches\transforms-4\df458c019b9b63d0ceef53ab6be68984\transformed\jetified-savedstate-1.2.1\res
com.autoflowmobile.app-lifecycle-livedata-core-2.6.2-34 C:\Users\<USER>\.gradle\caches\transforms-4\f3130b789f3cf2e64c68df79ac676864\transformed\lifecycle-livedata-core-2.6.2\res
com.autoflowmobile.app-jetified-lifecycle-viewmodel-savedstate-2.6.2-35 C:\Users\<USER>\.gradle\caches\transforms-4\f4df2576f4da951d41a70fb742baa12e\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\res
com.autoflowmobile.app-drawerlayout-1.1.1-36 C:\Users\<USER>\.gradle\caches\transforms-4\f4f2536877be8cc6f2b17e89b3ca6b4b\transformed\drawerlayout-1.1.1\res
com.autoflowmobile.app-pngs-37 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\generated\res\pngs\debug
com.autoflowmobile.app-resValues-38 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\generated\res\resValues\debug
com.autoflowmobile.app-packageDebugResources-39 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.autoflowmobile.app-packageDebugResources-40 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.autoflowmobile.app-debug-41 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.autoflowmobile.app-debug-42 C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\res
com.autoflowmobile.app-main-43 C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\res
com.autoflowmobile.app-debug-44 C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-45 C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-46 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-config\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-47 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-encrypted-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-48 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-fast-image\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-49 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-50 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-image-picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-51 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-52 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-53 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.autoflowmobile.app-debug-54 C:\Users\<USER>\Node\autoflow\mobile\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug\packageDebugResources
