com.autoflowmobile.app-jetified-core-ktx-1.9.0-0 C:\Users\<USER>\.gradle\caches\transforms-4\1010a7c217ecde968edb7bade6cd93d6\transformed\jetified-core-ktx-1.9.0\res
com.autoflowmobile.app-fragment-1.3.6-1 C:\Users\<USER>\.gradle\caches\transforms-4\1af68aa5549c7458847a29eaef0c53bc\transformed\fragment-1.3.6\res
com.autoflowmobile.app-sqlite-framework-2.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-4\27331e812418a7989ca19651231c3473\transformed\sqlite-framework-2.2.0\res
com.autoflowmobile.app-jetified-drawee-3.1.3-3 C:\Users\<USER>\.gradle\caches\transforms-4\4671ada73b10174dff6ecba084f7c23f\transformed\jetified-drawee-3.1.3\res
com.autoflowmobile.app-jetified-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\transforms-4\4f242b40d56bcfb89f4a8d05890ae23c\transformed\jetified-startup-runtime-1.1.1\res
com.autoflowmobile.app-jetified-emoji2-1.2.0-5 C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\res
com.autoflowmobile.app-jetified-lifecycle-process-2.4.1-6 C:\Users\<USER>\.gradle\caches\transforms-4\550a7d11c366654bf303111f5b687d65\transformed\jetified-lifecycle-process-2.4.1\res
com.autoflowmobile.app-jetified-react-android-0.73.2-debug-7 C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\res
com.autoflowmobile.app-jetified-emoji2-views-helper-1.2.0-8 C:\Users\<USER>\.gradle\caches\transforms-4\6113285c0e78220de35ac04d6845ed59\transformed\jetified-emoji2-views-helper-1.2.0\res
com.autoflowmobile.app-jetified-annotation-experimental-1.3.0-9 C:\Users\<USER>\.gradle\caches\transforms-4\733abbad5e9ea45dff5bb5afa3e66cdb\transformed\jetified-annotation-experimental-1.3.0\res
com.autoflowmobile.app-sqlite-2.2.0-10 C:\Users\<USER>\.gradle\caches\transforms-4\8d7db3777ac9373bfbede945aa86d14d\transformed\sqlite-2.2.0\res
com.autoflowmobile.app-appcompat-1.6.1-11 C:\Users\<USER>\.gradle\caches\transforms-4\8f3cc09420e8ccfe99f443c7659aadc9\transformed\appcompat-1.6.1\res
com.autoflowmobile.app-jetified-activity-1.6.0-12 C:\Users\<USER>\.gradle\caches\transforms-4\90f619982098bc314ed9d8ded9ea203a\transformed\jetified-activity-1.6.0\res
com.autoflowmobile.app-jetified-lifecycle-viewmodel-savedstate-2.5.1-13 C:\Users\<USER>\.gradle\caches\transforms-4\94b3c12436ab64c8d1a9d0e1f4f4d8b6\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\res
com.autoflowmobile.app-lifecycle-runtime-2.5.1-14 C:\Users\<USER>\.gradle\caches\transforms-4\95cf405b99f234e3085aecc5a4fac12e\transformed\lifecycle-runtime-2.5.1\res
com.autoflowmobile.app-jetified-flipper-0.201.0-15 C:\Users\<USER>\.gradle\caches\transforms-4\98922406aa30e38ed245b1e87bdedd94\transformed\jetified-flipper-0.201.0\res
com.autoflowmobile.app-lifecycle-livedata-core-2.5.1-16 C:\Users\<USER>\.gradle\caches\transforms-4\9b41e66cbeb70cb214ad2facf9d14ef5\transformed\lifecycle-livedata-core-2.5.1\res
com.autoflowmobile.app-jetified-savedstate-1.2.0-17 C:\Users\<USER>\.gradle\caches\transforms-4\a99f88b96c6bc8545047d0da64238489\transformed\jetified-savedstate-1.2.0\res
com.autoflowmobile.app-jetified-appcompat-resources-1.6.1-18 C:\Users\<USER>\.gradle\caches\transforms-4\ac726015019e861dce070d698b39c109\transformed\jetified-appcompat-resources-1.6.1\res
com.autoflowmobile.app-jetified-tracing-1.1.0-19 C:\Users\<USER>\.gradle\caches\transforms-4\c81e3d305b78a2b485d84341baedf907\transformed\jetified-tracing-1.1.0\res
com.autoflowmobile.app-jetified-autofill-1.1.0-20 C:\Users\<USER>\.gradle\caches\transforms-4\cd542cf04f0173883ff2e0ddfee08a56\transformed\jetified-autofill-1.1.0\res
com.autoflowmobile.app-swiperefreshlayout-1.1.0-21 C:\Users\<USER>\.gradle\caches\transforms-4\da3e3798b330ef3e05862728f0b02188\transformed\swiperefreshlayout-1.1.0\res
com.autoflowmobile.app-lifecycle-viewmodel-2.5.1-22 C:\Users\<USER>\.gradle\caches\transforms-4\e523bb19e58b9e85b971b87af844b73c\transformed\lifecycle-viewmodel-2.5.1\res
com.autoflowmobile.app-core-1.9.0-23 C:\Users\<USER>\.gradle\caches\transforms-4\f78e616298a04923dd5d4d62c52d50b0\transformed\core-1.9.0\res
com.autoflowmobile.app-pngs-24 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\generated\res\pngs\debug
com.autoflowmobile.app-resValues-25 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\generated\res\resValues\debug
com.autoflowmobile.app-packageDebugResources-26 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.autoflowmobile.app-packageDebugResources-27 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.autoflowmobile.app-debug-28 C:\Users\<USER>\Node\autoflow\mobile\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.autoflowmobile.app-debug-29 C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\res
com.autoflowmobile.app-main-30 C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\res
