{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "2,35,45,46,47,49,50,52,55,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3131,4143,4244,4365,4528,4594,4759,4988,5048,5132,5198,5256,5329,5392,5448,5567,5624,5685,5741,5815,6188,6274,6358,6461,6543,6626,6716,6783,6849,6922,7000,7088,7159,7236,7310,7382,7473,7547,7642,7740,7814,7894,7995,8048,8114,8203,8293,8355,8419,8482,8594,8707,8817,8929", "endLines": "6,35,45,46,47,49,50,52,55,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "321,3213,4239,4360,4444,4589,4684,4828,5043,5127,5193,5251,5324,5387,5443,5562,5619,5680,5736,5810,5955,6269,6353,6456,6538,6621,6711,6778,6844,6917,6995,7083,7154,7231,7305,7377,7468,7542,7637,7735,7809,7889,7990,8043,8109,8198,8288,8350,8414,8477,8589,8702,8812,8924,9003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,232,323,402,472,543,627,695,771,855,940,1030,1099,1183,1273,1348,1430,1509,1587,1665,1739,1824,1897,1973", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,227,318,397,467,538,622,690,766,850,935,1025,1094,1178,1268,1343,1425,1504,1582,1660,1734,1819,1892,1968,2053"}, "to": {"startLines": "34,43,44,48,51,53,54,68,69,70,105,106,107,108,110,111,112,113,114,115,116,117,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3061,3945,4052,4449,4689,4833,4904,5960,6028,6104,9008,9093,9183,9252,9418,9508,9583,9665,9744,9822,9900,9974,10160,10233,10309", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3126,4047,4138,4523,4754,4899,4983,6023,6099,6183,9088,9178,9247,9331,9503,9578,9660,9739,9817,9895,9969,10054,10228,10304,10389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "36,37,38,39,40,41,42,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3218,3316,3418,3518,3617,3719,3828,10059", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3311,3413,3513,3612,3714,3823,3940,10155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,449,555,668,752,857,976,1061,1141,1232,1325,1420,1514,1614,1707,1802,1896,1987,2079,2160,2270,2378,2476,2588,2694,2798,2960,9336", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "444,550,663,747,852,971,1056,1136,1227,1320,1415,1509,1609,1702,1797,1891,1982,2074,2155,2265,2373,2471,2583,2689,2793,2955,3056,9413"}}]}]}