{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-35:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,232,323,402,472,543,627,695,771,855,940,1030,1099,1183,1273,1348,1430,1509,1587,1665,1739,1824,1897,1973", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,227,318,397,467,538,622,690,766,850,935,1025,1094,1178,1268,1343,1425,1504,1582,1660,1734,1819,1892,1968,2053"}, "to": {"startLines": "34,40,41,45,48,50,51,66,67,68,106,107,108,109,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3117,3640,3747,4144,4384,4528,4599,5717,5785,5861,8948,9033,9123,9192,9360,9450,9525,9607,9686,9764,9842,9916,10102,10175,10251", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3182,3742,3833,4218,4449,4594,4678,5780,5856,5940,9028,9118,9187,9271,9445,9520,9602,9681,9759,9837,9911,9996,10170,10246,10331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,9276", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,9355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\603752beef54a2060376812c2cf342db\\transformed\\material-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2907,2962,3013,3079,3152,3230,3318,3389,3466,3540,3612,3703,3777,3872,3970,4044,4124,4225,4278,4344,4433,4523,4585,4649,4712,4824,4937,5047,5159,5218,5273", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,89,54,50,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2902,2957,3008,3074,3147,3225,3313,3384,3461,3535,3607,3698,3772,3867,3965,4039,4119,4220,4273,4339,4428,4518,4580,4644,4707,4819,4932,5042,5154,5213,5268,5347"}, "to": {"startLines": "2,35,36,37,38,39,42,43,44,46,47,49,52,53,54,55,56,57,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3187,3279,3367,3454,3550,3838,3939,4060,4223,4289,4454,4683,4743,4827,4889,4955,5013,5086,5149,5205,5324,5381,5442,5498,5572,5945,6031,6115,6248,6330,6413,6503,6558,6609,6675,6748,6826,6914,6985,7062,7136,7208,7299,7373,7468,7566,7640,7720,7821,7874,7940,8029,8119,8181,8245,8308,8420,8533,8643,8755,8814,8869", "endLines": "6,35,36,37,38,39,42,43,44,46,47,49,52,53,54,55,56,57,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,89,54,50,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,58,54,78", "endOffsets": "366,3274,3362,3449,3545,3635,3934,4055,4139,4284,4379,4523,4738,4822,4884,4950,5008,5081,5144,5200,5319,5376,5437,5493,5567,5712,6026,6110,6243,6325,6408,6498,6553,6604,6670,6743,6821,6909,6980,7057,7131,7203,7294,7368,7463,7561,7635,7715,7816,7869,7935,8024,8114,8176,8240,8303,8415,8528,8638,8750,8809,8864,8943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "10001", "endColumns": "100", "endOffsets": "10097"}}]}]}