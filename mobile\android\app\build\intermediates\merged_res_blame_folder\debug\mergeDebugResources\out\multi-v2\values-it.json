{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,300,399,539,622,688,783,868,930,1018,1087,1150,1223,1286,1340,1461,1518,1580,1634,1711,1848,1933,2015,2120,2201,2282,2373,2440,2506,2579,2659,2750,2825,2902,2971,3048,3136,3225,3318,3411,3485,3565,3659,3710,3776,3860,3948,4010,4074,4137,4252,4362,4468,4577", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "218,295,394,534,617,683,778,863,925,1013,1082,1145,1218,1281,1335,1456,1513,1575,1629,1706,1843,1928,2010,2115,2196,2277,2368,2435,2501,2574,2654,2745,2820,2897,2966,3043,3131,3220,3313,3406,3480,3560,3654,3705,3771,3855,3943,4005,4069,4132,4247,4357,4463,4572,4652"}, "to": {"startLines": "2,34,44,45,46,48,49,51,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3037,4062,4161,4301,4468,4534,4700,4945,5007,5095,5164,5227,5300,5363,5417,5538,5595,5657,5711,5788,6089,6174,6256,6361,6442,6523,6614,6681,6747,6820,6900,6991,7066,7143,7212,7289,7377,7466,7559,7652,7726,7806,7900,7951,8017,8101,8189,8251,8315,8378,8493,8603,8709,8818", "endLines": "5,34,44,45,46,48,49,51,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "268,3109,4156,4296,4379,4529,4624,4780,5002,5090,5159,5222,5295,5358,5412,5533,5590,5652,5706,5783,5920,6169,6251,6356,6437,6518,6609,6676,6742,6815,6895,6986,7061,7138,7207,7284,7372,7461,7554,7647,7721,7801,7895,7946,8012,8096,8184,8246,8310,8373,8488,8598,8704,8813,8893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "35,36,37,38,39,40,41,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3114,3212,3314,3413,3515,3624,3731,9865", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3207,3309,3408,3510,3619,3726,3856,9961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,378,481,590,674,779,898,976,1051,1143,1237,1330,1424,1525,1619,1716,1811,1903,1995,2076,2182,2289,2387,2491,2597,2704,2867,9157", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "373,476,585,669,774,893,971,1046,1138,1232,1325,1419,1520,1614,1711,1806,1898,1990,2071,2177,2284,2382,2486,2592,2699,2862,2962,9234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,233,326,410,481,553,641,721,805,895,976,1064,1150,1227,1307,1386,1461,1531,1600,1690,1765,1846", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,228,321,405,476,548,636,716,800,890,971,1059,1145,1222,1302,1381,1456,1526,1595,1685,1760,1841,1928"}, "to": {"startLines": "33,42,43,47,50,52,53,67,68,103,104,105,107,108,109,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2967,3861,3969,4384,4629,4785,4857,5925,6005,8898,8988,9069,9239,9325,9402,9482,9561,9636,9706,9775,9966,10041,10122", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3032,3964,4057,4463,4695,4852,4940,6000,6084,8983,9064,9152,9320,9397,9477,9556,9631,9701,9770,9860,10036,10117,10204"}}]}]}