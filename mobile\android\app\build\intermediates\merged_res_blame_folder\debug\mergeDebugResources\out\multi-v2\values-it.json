{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-35:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\603752beef54a2060376812c2cf342db\\transformed\\material-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2869,2924,2975,3041,3114,3194,3285,3360,3437,3506,3583,3671,3760,3853,3946,4020,4100,4194,4245,4311,4395,4483,4545,4609,4672,4787,4897,5003,5112,5171,5226", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2864,2919,2970,3036,3109,3189,3280,3355,3432,3501,3578,3666,3755,3848,3941,4015,4095,4189,4240,4306,4390,4478,4540,4604,4667,4782,4892,4998,5107,5166,5221,5301"}, "to": {"startLines": "2,34,35,36,37,38,41,42,43,45,46,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3082,3166,3247,3324,3423,3719,3818,3958,4125,4191,4357,4602,4664,4752,4814,4883,4946,5019,5082,5136,5257,5314,5376,5430,5507,5808,5893,5975,6110,6191,6272,6363,6418,6469,6535,6608,6688,6779,6854,6931,7000,7077,7165,7254,7347,7440,7514,7594,7688,7739,7805,7889,7977,8039,8103,8166,8281,8391,8497,8606,8665,8720", "endLines": "5,34,35,36,37,38,41,42,43,45,46,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,90,54,50,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,58,54,79", "endOffsets": "313,3161,3242,3319,3418,3513,3813,3953,4036,4186,4281,4437,4659,4747,4809,4878,4941,5014,5077,5131,5252,5309,5371,5425,5502,5639,5888,5970,6105,6186,6267,6358,6413,6464,6530,6603,6683,6774,6849,6926,6995,7072,7160,7249,7342,7435,7509,7589,7683,7734,7800,7884,7972,8034,8098,8161,8276,8386,8492,8601,8660,8715,8795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,9059", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,9136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "9767", "endColumns": "100", "endOffsets": "9863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,233,326,410,481,553,641,721,805,895,976,1064,1150,1227,1307,1386,1461,1531,1600,1690,1765,1846", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,228,321,405,476,548,636,716,800,890,971,1059,1145,1222,1302,1381,1456,1526,1595,1685,1760,1841,1928"}, "to": {"startLines": "33,39,40,44,47,49,50,65,66,104,105,106,108,109,110,111,112,113,114,115,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,3518,3626,4041,4286,4442,4514,5644,5724,8800,8890,8971,9141,9227,9304,9384,9463,9538,9608,9677,9868,9943,10024", "endColumns": "69,107,92,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,3621,3714,4120,4352,4509,4597,5719,5803,8885,8966,9054,9222,9299,9379,9458,9533,9603,9672,9762,9938,10019,10106"}}]}]}