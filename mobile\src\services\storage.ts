// Simple in-memory storage service for demo purposes
// In production, this would use AsyncStorage or other persistent storage

class SimpleStorageService {
  private storage: { [key: string]: string } = {};

  async setItem(key: string, value: string): Promise<void> {
    this.storage[key] = value;
    console.log(`Storage: Set ${key} = ${value}`);
  }

  async getItem(key: string): Promise<string | null> {
    const value = this.storage[key] || null;
    console.log(`Storage: Get ${key} = ${value}`);
    return value;
  }

  async removeItem(key: string): Promise<void> {
    delete this.storage[key];
    console.log(`Storage: Removed ${key}`);
  }

  async clear(): Promise<void> {
    this.storage = {};
    console.log('Storage: Cleared all data');
  }

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    return Object.keys(this.storage);
  }

  // Check if key exists
  async hasItem(key: string): Promise<boolean> {
    return key in this.storage;
  }
}

export const simpleStorage = new SimpleStorageService();
export default simpleStorage;
