// Persistent storage service using AsyncStorage
// This provides true persistence across app restarts

import AsyncStorage from '@react-native-async-storage/async-storage';

class SimpleStorageService {
  constructor() {
    console.log('Storage: Initialized AsyncStorage persistent storage');
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
      console.log(`Storage: Set ${key} = ${value} (persisted to AsyncStorage)`);
    } catch (error) {
      console.error(`Storage: Error setting ${key}:`, error);
      throw error;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      console.log(`Storage: Get ${key} = ${value} (from AsyncStorage)`);
      return value;
    } catch (error) {
      console.error(`Storage: Error getting ${key}:`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
      console.log(`Storage: Removed ${key} (from AsyncStorage)`);
    } catch (error) {
      console.error(`Storage: Error removing ${key}:`, error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
      console.log('Storage: Cleared all data (from AsyncStorage)');
    } catch (error) {
      console.error('Storage: Error clearing data:', error);
      throw error;
    }
  }

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      console.log('Storage: All keys from AsyncStorage:', keys);
      return keys;
    } catch (error) {
      console.error('Storage: Error getting all keys:', error);
      return [];
    }
  }

  // Check if key exists
  async hasItem(key: string): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(key);
      const exists = value !== null;
      console.log(`Storage: ${key} exists in AsyncStorage: ${exists}`);
      return exists;
    } catch (error) {
      console.error(`Storage: Error checking if ${key} exists:`, error);
      return false;
    }
  }
}

export const simpleStorage = new SimpleStorageService();
export default simpleStorage;
