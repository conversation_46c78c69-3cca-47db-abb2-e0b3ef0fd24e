// Demo storage service that simulates persistence
// For demo purposes, this maintains state across app restarts

// Global storage that simulates persistence (in real app, this would be AsyncStorage)
let globalPersistentStorage: { [key: string]: string } = {};

class SimpleStorageService {
  private storage: { [key: string]: string } = {};
  private initialized = false;

  // Initialize storage by loading from "persistent" storage
  private async initialize(): Promise<void> {
    if (this.initialized) return;

    console.log('Storage: Initializing with persistent data...');

    // Load from global persistent storage (simulates AsyncStorage)
    this.storage = { ...globalPersistentStorage };

    this.initialized = true;
    console.log('Storage: Loaded persistent data:', Object.keys(this.storage));
  }

  async setItem(key: string, value: string): Promise<void> {
    await this.initialize();
    this.storage[key] = value;

    // Also save to "persistent" storage
    globalPersistentStorage[key] = value;

    console.log(`Storage: Set ${key} = ${value} (persisted)`);
  }

  async getItem(key: string): Promise<string | null> {
    await this.initialize();
    const value = this.storage[key] || null;
    console.log(`Storage: Get ${key} = ${value}`);
    return value;
  }

  async removeItem(key: string): Promise<void> {
    await this.initialize();
    delete this.storage[key];
    delete globalPersistentStorage[key];
    console.log(`Storage: Removed ${key} (from persistent storage too)`);
  }

  async clear(): Promise<void> {
    await this.initialize();
    this.storage = {};
    globalPersistentStorage = {};
    console.log('Storage: Cleared all data (including persistent)');
  }

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    await this.initialize();
    return Object.keys(this.storage);
  }

  // Check if key exists
  async hasItem(key: string): Promise<boolean> {
    await this.initialize();
    return key in this.storage;
  }
}

export const simpleStorage = new SimpleStorageService();
export default simpleStorage;
