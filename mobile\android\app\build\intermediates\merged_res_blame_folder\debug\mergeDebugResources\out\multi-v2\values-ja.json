{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-39:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9d2e7d5dda1637c878ef743014c0f3e1\\transformed\\core-1.16.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "35,36,37,38,39,40,41,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2981,3073,3173,3267,3363,3456,3549,9133", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3068,3168,3262,3358,3451,3544,3645,9229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5a6ad47aa42d204c405611bf0090dcea\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "258,355,448,553,635,733,841,919,994,1085,1178,1273,1367,1467,1560,1655,1749,1840,1931,2009,2111,2209,2304,2407,2503,2599,2747,8465", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "350,443,548,630,728,836,914,989,1080,1173,1268,1362,1462,1555,1650,1744,1835,1926,2004,2106,2204,2299,2402,2498,2594,2742,2839,8539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,218,301,375,443,509,585,651,718,790,865,941,1017,1084,1159,1234,1306,1383,1459,1531,1601,1670,1748,1816,1887,1955", "endColumns": "67,94,82,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "118,213,296,370,438,504,580,646,713,785,860,936,1012,1079,1154,1229,1301,1378,1454,1526,1596,1665,1743,1811,1882,1950,2021"}, "to": {"startLines": "33,42,43,47,50,52,53,55,68,69,70,105,106,107,108,110,111,112,113,114,115,116,117,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2844,3650,3745,4093,4314,4446,4512,4646,5508,5575,5647,8171,8247,8323,8390,8544,8619,8691,8768,8844,8916,8986,9055,9234,9302,9373,9441", "endColumns": "67,94,82,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "2907,3740,3823,4162,4377,4507,4583,4707,5570,5642,5717,8242,8318,8385,8460,8614,8686,8763,8839,8911,8981,9050,9128,9297,9368,9436,9507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,277,362,466,542,605,689,753,811,892,956,1011,1070,1127,1181,1274,1330,1387,1441,1507,1607,1683,1764,1856,1918,1980,2059,2126,2192,2262,2332,2409,2473,2544,2612,2675,2754,2817,2897,2979,3051,3122,3194,3242,3306,3381,3458,3520,3584,3647,3733,3817,3898,3983", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "203,272,357,461,537,600,684,748,806,887,951,1006,1065,1122,1176,1269,1325,1382,1436,1502,1602,1678,1759,1851,1913,1975,2054,2121,2187,2257,2327,2404,2468,2539,2607,2670,2749,2812,2892,2974,3046,3117,3189,3237,3301,3376,3453,3515,3579,3642,3728,3812,3893,3978,4051"}, "to": {"startLines": "2,34,44,45,46,48,49,51,54,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2912,3828,3913,4017,4167,4230,4382,4588,4712,4793,4857,4912,4971,5028,5082,5175,5231,5288,5342,5408,5722,5798,5879,5971,6033,6095,6174,6241,6307,6377,6447,6524,6588,6659,6727,6790,6869,6932,7012,7094,7166,7237,7309,7357,7421,7496,7573,7635,7699,7762,7848,7932,8013,8098", "endLines": "5,34,44,45,46,48,49,51,54,56,57,58,59,60,61,62,63,64,65,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "253,2976,3908,4012,4088,4225,4309,4441,4641,4788,4852,4907,4966,5023,5077,5170,5226,5283,5337,5403,5503,5793,5874,5966,6028,6090,6169,6236,6302,6372,6442,6519,6583,6654,6722,6785,6864,6927,7007,7089,7161,7232,7304,7352,7416,7491,7568,7630,7694,7757,7843,7927,8008,8093,8166"}}]}]}