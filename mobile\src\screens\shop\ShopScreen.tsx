import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { COLORS } from '../../constants/theme';
import PartsListing from '../../components/PartsListing';
import { partsService } from '../../services/parts';
import { Part } from '../../types';

export const ShopScreen: React.FC = () => {
  const [parts, setParts] = useState<Part[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Load parts from API
  useEffect(() => {
    loadParts();
  }, []);

  const loadParts = async (pageNum: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      const response = await partsService.getParts(pageNum, 12); // 12 parts per page

      if (response.data) {
        const newParts = response.data.map(part => ({
          id: part.id,
          title: part.title || part.name || 'Unknown Part',
          price: `KSh ${part.price?.toLocaleString() || '0'}`,
          discountedPrice: part.original_price && part.original_price > part.price
            ? `KSh ${part.original_price.toLocaleString()}`
            : undefined,
          condition: part.condition || 'Unknown',
          stock: part.stock || 0,
          thumbnailUrl: part.thumbnail_url || part.image_url || null,
        }));

        if (append) {
          setParts(prev => [...prev, ...newParts]);
        } else {
          setParts(newParts);
        }

        setHasMore(response.pagination?.hasMore || false);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading parts:', error);
      Alert.alert(
        'Error',
        'Failed to load parts. Please check your internet connection and try again.',
        [
          { text: 'Retry', onPress: () => loadParts(pageNum, append) },
          { text: 'Cancel', style: 'cancel' }
        ]
      );

      // Fallback to mock data if API fails
      if (!append && parts.length === 0) {
        setParts(getMockParts());
      }
    } finally {
      setLoading(false);
    }
  };

  // Fallback mock data
  const getMockParts = () => [
    {
      id: 1,
      title: 'Volkswagen (VW) Audi Brake Pad Set 1K0698151 Compatible with Golf, A3, Passat',
      price: 'KSh 4,500',
      discountedPrice: 'KSh 5,200',
      condition: 'New',
      stock: 5,
      thumbnailUrl: null,
    },
    {
      id: 2,
      title: 'VW Golf Engine Oil Filter 06A115561B Compatible with Golf MK5, MK6',
      price: 'KSh 1,200',
      condition: 'New',
      stock: 12,
      thumbnailUrl: null,
    },
    {
      id: 3,
      title: 'Audi A3 Headlight Assembly 8P0941003 Compatible with A3 8P',
      price: 'KSh 15,000',
      condition: 'Used',
      stock: 2,
      thumbnailUrl: null,
    },
  ];
  const handlePartPress = (partId: number) => {
    console.log('Part pressed:', partId);
    // TODO: Navigate to part details
  };

  const handleCallPress = (partId: number) => {
    console.log('Call pressed for part:', partId);
    // TODO: Implement call functionality
  };

  const handleWhatsAppPress = (partId: number) => {
    console.log('WhatsApp pressed for part:', partId);
    // TODO: Implement WhatsApp functionality
  };

  return (
    <View style={styles.container}>
      <PartsListing
        parts={parts}
        onPartPress={handlePartPress}
        onCallPress={handleCallPress}
        onWhatsAppPress={handleWhatsAppPress}
        title="Shop"
        subtitle={`${parts.length} parts available`}
        loading={loading}
        onRefresh={() => loadParts(1, false)}
        onLoadMore={hasMore ? () => loadParts(page + 1, true) : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
});
