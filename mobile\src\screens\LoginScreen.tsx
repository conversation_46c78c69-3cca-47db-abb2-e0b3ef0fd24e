import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { COLORS, SPACING, TYPOGRAPHY } from '../constants/theme';
import { biometricService } from '../services/biometric';
import AsyncStorage from '@react-native-async-storage/async-storage';

type Screen = 'Home' | 'Login' | 'Parts' | 'Profile';

interface Props {
  onNavigate: (screen: Screen) => void;
}

const LoginScreen: React.FC<Props> = ({ onNavigate }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricType, setBiometricType] = useState<string | null>(null);

  useEffect(() => {
    checkBiometricAvailability();
    checkBiometricEnabled();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const available = await biometricService.isBiometricAvailable();
      setBiometricAvailable(available);

      if (available) {
        const type = await biometricService.getBiometricType();
        setBiometricType(type);
      }
    } catch (error) {
      console.log('Error checking biometric availability:', error);
    }
  };

  const checkBiometricEnabled = async () => {
    try {
      const enabled = await biometricService.isBiometricEnabled();
      setBiometricEnabled(enabled);
    } catch (error) {
      console.log('Error checking biometric enabled status:', error);
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store user credentials for demo
      await AsyncStorage.setItem('user_email', email);
      await AsyncStorage.setItem('user_logged_in', 'true');

      // Ask about biometric setup if available and not already enabled
      if (biometricAvailable && !biometricEnabled) {
        Alert.alert(
          'Enable Biometric Login?',
          `Would you like to enable ${biometricType || 'biometric'} authentication for faster login next time?`,
          [
            {
              text: 'Not Now',
              style: 'cancel',
              onPress: () => onNavigate('Home'),
            },
            {
              text: 'Enable',
              onPress: () => enableBiometric(),
            },
          ]
        );
      } else {
        onNavigate('Home');
      }
    } catch (error) {
      Alert.alert('Error', 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const enableBiometric = async () => {
    try {
      await biometricService.enableBiometric(email, 'user_demo_id');
      Alert.alert(
        'Success!',
        `${biometricType || 'Biometric'} authentication has been enabled! You can now use it to login quickly.`,
        [{ text: 'OK', onPress: () => onNavigate('Home') }]
      );
    } catch (error) {
      console.log('Error enabling biometric:', error);
      Alert.alert(
        'Setup Failed',
        'Could not enable biometric authentication. You can try again later in settings.',
        [{ text: 'OK', onPress: () => onNavigate('Home') }]
      );
    }
  };

  const handleBiometricLogin = async () => {
    try {
      setLoading(true);

      const credentials = await biometricService.getBiometricCredentials();
      if (credentials) {
        await AsyncStorage.setItem('user_email', credentials.email);
        await AsyncStorage.setItem('user_logged_in', 'true');
        onNavigate('Home');
      }
    } catch (error: any) {
      console.log('Biometric login error:', error);

      if (error.message?.includes('cancelled') || error.message?.includes('canceled')) {
        // User cancelled, don't show error
        return;
      }

      Alert.alert(
        'Authentication Failed',
        'Biometric authentication failed. Please try again or use your password.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const getBiometricIcon = () => {
    switch (biometricType) {
      case 'FaceID':
        return '👤';
      case 'TouchID':
      case 'Fingerprint':
        return '👆';
      default:
        return '🔒';
    }
  };

  const getBiometricText = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'Login with Face ID';
      case 'TouchID':
        return 'Login with Touch ID';
      case 'Fingerprint':
        return 'Login with Fingerprint';
      default:
        return 'Login with Biometrics';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.logo}>AutoFlow</Text>
        <Text style={styles.subtitle}>VW & Audi Parts Specialist</Text>
      </View>

      {/* Biometric Login Section */}
      {biometricAvailable && biometricEnabled && (
        <View style={styles.biometricSection}>
          <TouchableOpacity
            style={styles.biometricButton}
            onPress={handleBiometricLogin}
            disabled={loading}
          >
            <Text style={styles.biometricIcon}>{getBiometricIcon()}</Text>
            <Text style={styles.biometricText}>{getBiometricText()}</Text>
          </TouchableOpacity>

          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>
        </View>
      )}

      {/* Login Form */}
      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          editable={!loading}
        />

        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          editable={!loading}
        />

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={handleLogin}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text style={styles.buttonText}>Login</Text>
          )}
        </TouchableOpacity>

        <Text style={styles.demoText}>
          Demo credentials are pre-filled. Just tap Login!
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    paddingTop: SPACING.xl * 2,
    paddingBottom: SPACING.xl,
  },
  logo: {
    fontSize: TYPOGRAPHY.sizes.xxxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  biometricSection: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  biometricButton: {
    backgroundColor: COLORS.SURFACE,
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    minWidth: 200,
  },
  biometricIcon: {
    fontSize: 32,
    marginBottom: SPACING.sm,
  },
  biometricText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.PRIMARY,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.lg,
    width: '100%',
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.BORDER,
  },
  dividerText: {
    marginHorizontal: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    width: '100%',
    alignSelf: 'center',
  },
  input: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 8,
    marginBottom: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.TEXT_PRIMARY,
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.lg,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: SPACING.md,
    minHeight: 50,
    justifyContent: 'center',
  },
  buttonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  buttonText: {
    color: COLORS.WHITE,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  demoText: {
    textAlign: 'center',
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.md,
    fontStyle: 'italic',
  },
});

export default LoginScreen;
